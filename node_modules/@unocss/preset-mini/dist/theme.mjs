export { colors } from './colors.mjs';
export { u as baseSize, A as blockSize, b as blur, n as borderRadius, o as boxShadow, j as breakpoints, G as containers, d as dropShadow, m as duration, f as fontFamily, a as fontSize, i as fontWeight, C as height, B as inlineSize, h as letterSpacing, l as lineHeight, k as lineWidth, E as maxBlockSize, D as maxHeight, F as maxInlineSize, y as maxWidth, p as media, q as preflightBase, r as ringWidth, s as spacing, c as textIndent, g as textShadow, e as textStrokeWidth, t as theme, v as verticalBreakpoints, x as width, w as wordSpacing, z as zIndex } from './shared/preset-mini.BsxsSmTL.mjs';
import './shared/preset-mini.DxJ0TbJb.mjs';
import './shared/preset-mini.DrfPDgwn.mjs';
import '@unocss/core';
import '@unocss/rule-utils';
