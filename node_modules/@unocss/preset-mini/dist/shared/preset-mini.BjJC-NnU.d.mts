import { CSSObject, Arrayable } from '@unocss/core';

interface ThemeAnimation {
    keyframes?: Record<string, string>;
    durations?: Record<string, string>;
    timingFns?: Record<string, string>;
    properties?: Record<string, object>;
    counts?: Record<string, string | number>;
    category?: Record<string, string>;
}
interface Colors {
    [key: string]: Colors & {
        DEFAULT?: string;
    } | string;
}
interface Theme {
    width?: Record<string, string>;
    height?: Record<string, string>;
    maxWidth?: Record<string, string>;
    maxHeight?: Record<string, string>;
    minWidth?: Record<string, string>;
    minHeight?: Record<string, string>;
    inlineSize?: Record<string, string>;
    blockSize?: Record<string, string>;
    maxInlineSize?: Record<string, string>;
    maxBlockSize?: Record<string, string>;
    minInlineSize?: Record<string, string>;
    minBlockSize?: Record<string, string>;
    borderRadius?: Record<string, string>;
    breakpoints?: Record<string, string>;
    verticalBreakpoints?: Record<string, string>;
    colors?: Colors;
    borderColor?: Colors;
    backgroundColor?: Colors;
    textColor?: Colors;
    shadowColor?: Colors;
    accentColor?: Colors;
    fontFamily?: Record<string, string>;
    fontSize?: Record<string, string | [string, string | CSSObject] | [string, string, string]>;
    fontWeight?: Record<string, string>;
    lineHeight?: Record<string, string>;
    letterSpacing?: Record<string, string>;
    wordSpacing?: Record<string, string>;
    boxShadow?: Record<string, string | string[]>;
    textIndent?: Record<string, string>;
    textShadow?: Record<string, string | string[]>;
    textStrokeWidth?: Record<string, string>;
    ringWidth?: Record<string, string>;
    lineWidth?: Record<string, string>;
    spacing?: Record<string, string>;
    duration?: Record<string, string>;
    aria?: Record<string, string>;
    data?: Record<string, string>;
    zIndex?: Record<string, string>;
    blur?: Record<string, string>;
    dropShadow?: Record<string, string | string[]>;
    easing?: Record<string, string>;
    transitionProperty?: Record<string, string>;
    media?: Record<string, string>;
    supports?: Record<string, string>;
    containers?: Record<string, string>;
    animation?: ThemeAnimation;
    gridAutoColumn?: Record<string, string>;
    gridAutoRow?: Record<string, string>;
    gridColumn?: Record<string, string>;
    gridRow?: Record<string, string>;
    gridTemplateColumn?: Record<string, string>;
    gridTemplateRow?: Record<string, string>;
    container?: {
        center?: boolean;
        padding?: string | Record<string, string>;
        maxWidth?: Record<string, string>;
    };
    /** Used to generate CSS custom properties placeholder in preflight */
    preflightRoot?: Arrayable<string>;
    preflightBase?: Record<string, string | number>;
}

export type { Colors as C, Theme as T, ThemeAnimation as a };
