export { s as alignments, a as appearance, E as aspectRatio, e as bgColors, b as borderStyles, c as borders, B as boxSizing, f as colorScheme, g as containerParent, P as cssProperty, O as cssVariables, j as flex, x as flexGridJustifiesAlignments, z as floats, J as fonts, k as gaps, l as grids, h as handlerBorderStyle, y as insets, n as justifies, G as margins, d as opacity, q as orders, o as outline, m as overflows, F as paddings, u as placements, p as positions, C as questionMark, r as rules, D as sizes, H as svgUtilities, K as tabSizes, t as textAligns, i as textDecorations, L as textIndents, N as textShadows, M as textStrokes, I as transitions, v as verticalAligns, w as willChange, A as zIndexes } from './shared/preset-mini.BbFVnwVO.mjs';
export { e as appearances, c as boxShadows, b as boxShadowsBase, k as breaks, g as contains, i as contentVisibility, j as contents, f as cursors, d as displays, q as fieldSizing, o as fontSmoothings, n as fontStyles, p as pointerEvents, h as resizes, r as ringBase, a as rings, l as textOverflows, m as textTransforms, t as textWraps, s as transformBase, x as transforms, u as userSelects, v as varEmpty, w as whitespaces } from './shared/preset-mini.DxJ0TbJb.mjs';
import './shared/preset-mini.DrfPDgwn.mjs';
import '@unocss/core';
import '@unocss/rule-utils';
