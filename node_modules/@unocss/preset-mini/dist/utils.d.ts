import * as _unocss_rule_utils from '@unocss/rule-utils';
export * from '@unocss/rule-utils';
export { C as CONTROL_MINI_NO_NEGATIVE, c as colorResolver, a as colorableShadows, d as directionSize, h as hasParseableColor, i as isCSSMathFn, e as isSize, m as makeGlobalStaticRules, p as parseColor, r as resolveBreakpoints, b as resolveVerticalBreakpoints, s as splitShorthand, t as transformXYZ } from './shared/preset-mini.CRliz1QB.js';
import '@unocss/core';
import './shared/preset-mini.BjJC-NnU.js';

declare function numberWithUnit(str: string): string | undefined;
declare function auto(str: string): "auto" | undefined;
declare function rem(str: string): string | undefined;
declare function px(str: string): string | undefined;
declare function number(str: string): number | undefined;
declare function percent(str: string): string | undefined;
declare function fraction(str: string): string | undefined;
declare function bracket(str: string): string | undefined;
declare function bracketOfColor(str: string): string | undefined;
declare function bracketOfLength(str: string): string | undefined;
declare function bracketOfPosition(str: string): string | undefined;
declare function cssvar(str: string): string | undefined;
declare function time(str: string): string | undefined;
declare function degree(str: string): string | undefined;
declare function global(str: string): string | undefined;
declare function properties(str: string): string | undefined;
declare function position(str: string): string | undefined;

declare const handlers_auto: typeof auto;
declare const handlers_bracket: typeof bracket;
declare const handlers_bracketOfColor: typeof bracketOfColor;
declare const handlers_bracketOfLength: typeof bracketOfLength;
declare const handlers_bracketOfPosition: typeof bracketOfPosition;
declare const handlers_cssvar: typeof cssvar;
declare const handlers_degree: typeof degree;
declare const handlers_fraction: typeof fraction;
declare const handlers_global: typeof global;
declare const handlers_number: typeof number;
declare const handlers_numberWithUnit: typeof numberWithUnit;
declare const handlers_percent: typeof percent;
declare const handlers_position: typeof position;
declare const handlers_properties: typeof properties;
declare const handlers_px: typeof px;
declare const handlers_rem: typeof rem;
declare const handlers_time: typeof time;
declare namespace handlers {
  export {
    handlers_auto as auto,
    handlers_bracket as bracket,
    handlers_bracketOfColor as bracketOfColor,
    handlers_bracketOfLength as bracketOfLength,
    handlers_bracketOfPosition as bracketOfPosition,
    handlers_cssvar as cssvar,
    handlers_degree as degree,
    handlers_fraction as fraction,
    handlers_global as global,
    handlers_number as number,
    handlers_numberWithUnit as numberWithUnit,
    handlers_percent as percent,
    handlers_position as position,
    handlers_properties as properties,
    handlers_px as px,
    handlers_rem as rem,
    handlers_time as time,
  };
}

declare const handler: _unocss_rule_utils.ValueHandler<"number" | "numberWithUnit" | "auto" | "rem" | "px" | "percent" | "fraction" | "bracket" | "bracketOfColor" | "bracketOfLength" | "bracketOfPosition" | "cssvar" | "time" | "degree" | "global" | "properties" | "position">;
declare const h: _unocss_rule_utils.ValueHandler<"number" | "numberWithUnit" | "auto" | "rem" | "px" | "percent" | "fraction" | "bracket" | "bracketOfColor" | "bracketOfLength" | "bracketOfPosition" | "cssvar" | "time" | "degree" | "global" | "properties" | "position">;

declare const directionMap: Record<string, string[]>;
declare const insetMap: Record<string, string[]>;
declare const cornerMap: Record<string, string[]>;
declare const xyzMap: Record<string, string[]>;
declare const xyzArray: string[];
declare const positionMap: Record<string, string>;
declare const globalKeywords: string[];
declare const cssMathFnRE: RegExp;
declare const cssVarFnRE: RegExp;

export { cornerMap, cssMathFnRE, cssVarFnRE, directionMap, globalKeywords, h, handler, insetMap, positionMap, handlers as valueHandlers, xyzArray, xyzMap };
