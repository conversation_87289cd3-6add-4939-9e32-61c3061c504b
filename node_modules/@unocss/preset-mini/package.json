{"name": "@unocss/preset-mini", "type": "module", "version": "66.5.1", "description": "The minimal preset for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-presets/preset-mini"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./theme": {"types": "./dist/theme.d.mts", "default": "./dist/theme.mjs"}, "./variants": {"types": "./dist/variants.d.mts", "default": "./dist/variants.mjs"}, "./rules": {"types": "./dist/rules.d.mts", "default": "./dist/rules.mjs"}, "./colors": {"types": "./dist/colors.d.mts", "default": "./dist/colors.mjs"}, "./utils": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}, "./*": "./*"}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["*.css", "*.d.ts", "dist"], "dependencies": {"@unocss/core": "66.5.1", "@unocss/rule-utils": "66.5.1", "@unocss/extractor-arbitrary-variants": "66.5.1"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}