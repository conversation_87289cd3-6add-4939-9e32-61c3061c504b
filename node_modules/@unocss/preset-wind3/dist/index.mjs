import { toArray, definePreset } from '@unocss/core';
import presetMini, { presetMini as presetMini$1 } from '@unocss/preset-mini';
export { colors, preflights } from '@unocss/preset-mini';
import { shortcuts } from './shortcuts.mjs';
import { theme } from './theme.mjs';
import { b as variants } from './shared/preset-wind3.DIaVUBWk.mjs';
import { r as rules } from './shared/preset-wind3.B6YTuXU2.mjs';
import './shared/preset-wind3.BzmnsdqZ.mjs';
import '@unocss/preset-mini/utils';
import '@unocss/preset-mini/rules';
import '@unocss/preset-mini/theme';
import './shared/preset-wind3.DjKJQ_OR.mjs';
import '@unocss/preset-mini/variants';
import '@unocss/rule-utils';

function important(option) {
  if (option == null || option === false)
    return [];
  const wrapWithIs = (selector) => {
    if (selector.startsWith(":is(") && selector.endsWith(")"))
      return selector;
    if (selector.includes("::"))
      return selector.replace(/(.*?)((?:\s\*)?::.*)/, ":is($1)$2");
    return `:is(${selector})`;
  };
  return [
    option === true ? (util) => {
      util.entries.forEach((i) => {
        if (i[1] != null && !String(i[1]).endsWith("!important"))
          i[1] += " !important";
      });
    } : (util) => {
      if (!util.selector.startsWith(option))
        util.selector = `${option} ${wrapWithIs(util.selector)}`;
    }
  ];
}

function postprocessors(options) {
  return [
    ...toArray(presetMini(options).postprocess),
    ...important(options.important)
  ];
}

const presetWind3 = definePreset((options = {}) => {
  options.important = options.important ?? false;
  return {
    ...presetMini$1(options),
    name: "@unocss/preset-wind3",
    theme,
    rules,
    shortcuts,
    variants: variants(options),
    postprocess: postprocessors(options)
  };
});

export { presetWind3 as default, presetWind3 as presetWind, presetWind3, rules, shortcuts, theme, variants };
