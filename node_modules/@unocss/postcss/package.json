{"name": "@unocss/postcss", "type": "module", "version": "66.5.1", "description": "PostCSS plugin for UnoCSS", "author": "sibbng <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-integrations/postcss"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./esm": {"types": "./dist/esm.d.mts", "default": "./dist/esm.mjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/*", "./*"]}}, "files": ["dist"], "engines": {"node": ">=14"}, "peerDependencies": {"postcss": "^8.4.21"}, "dependencies": {"css-tree": "^3.1.0", "postcss": "^8.5.6", "tinyglobby": "^0.2.14", "@unocss/config": "66.5.1", "@unocss/rule-utils": "66.5.1", "@unocss/core": "66.5.1"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}