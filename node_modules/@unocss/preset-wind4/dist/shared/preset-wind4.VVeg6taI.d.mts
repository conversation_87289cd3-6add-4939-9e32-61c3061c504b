declare const theme: {
    font: {
        sans: string;
        serif: string;
        mono: string;
    };
    colors: {
        black: string;
        white: string;
        slate: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        gray: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        zinc: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        neutral: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        stone: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        red: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        orange: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        amber: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        yellow: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        lime: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        green: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        emerald: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        teal: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        cyan: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        sky: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        blue: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        indigo: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        violet: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        purple: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        fuchsia: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        pink: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        rose: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        light: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
        dark: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
            950: string;
        };
    };
    spacing: {
        DEFAULT: string;
        xs: string;
        sm: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
        '4xl': string;
        '5xl': string;
        '6xl': string;
        '7xl': string;
        '8xl': string;
        '9xl': string;
    };
    breakpoint: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
    };
    verticalBreakpoint: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
    };
    text: {
        xs: {
            fontSize: string;
            lineHeight: string;
        };
        sm: {
            fontSize: string;
            lineHeight: string;
        };
        base: {
            fontSize: string;
            lineHeight: string;
        };
        lg: {
            fontSize: string;
            lineHeight: string;
        };
        xl: {
            fontSize: string;
            lineHeight: string;
        };
        '2xl': {
            fontSize: string;
            lineHeight: string;
        };
        '3xl': {
            fontSize: string;
            lineHeight: string;
        };
        '4xl': {
            fontSize: string;
            lineHeight: string;
        };
        '5xl': {
            fontSize: string;
            lineHeight: string;
        };
        '6xl': {
            fontSize: string;
            lineHeight: string;
        };
        '7xl': {
            fontSize: string;
            lineHeight: string;
        };
        '8xl': {
            fontSize: string;
            lineHeight: string;
        };
        '9xl': {
            fontSize: string;
            lineHeight: string;
        };
    };
    fontWeight: {
        thin: string;
        extralight: string;
        light: string;
        normal: string;
        medium: string;
        semibold: string;
        bold: string;
        extrabold: string;
        black: string;
    };
    tracking: {
        tighter: string;
        tight: string;
        normal: string;
        wide: string;
        wider: string;
        widest: string;
    };
    leading: {
        none: string;
        tight: string;
        snug: string;
        normal: string;
        relaxed: string;
        loose: string;
    };
    textStrokeWidth: Record<string, string> | undefined;
    radius: {
        DEFAULT: string;
        none: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
        '4xl': string;
    };
    shadow: {
        DEFAULT: string[];
        '2xs': string;
        xs: string;
        sm: string[];
        md: string[];
        lg: string[];
        xl: string[];
        '2xl': string;
        none: string;
        inner: string;
    };
    insetShadow: {
        '2xs': string;
        xs: string;
        sm: string;
        none: string;
    };
    dropShadow: {
        DEFAULT: string[];
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
    };
    textShadow: {
        none: string;
        '2xs': string;
        xs: string;
        sm: string[];
        md: string[];
        lg: string[];
    };
    ease: {
        linear: string;
        in: string;
        out: string;
        'in-out': string;
        DEFAULT: string;
    };
    animation: {
        keyframes: {
            pulse: string;
            bounce: string;
            spin: string;
            ping: string;
            'bounce-alt': string;
            flash: string;
            'pulse-alt': string;
            'rubber-band': string;
            'shake-x': string;
            'shake-y': string;
            'head-shake': string;
            swing: string;
            tada: string;
            wobble: string;
            jello: string;
            'heart-beat': string;
            hinge: string;
            'jack-in-the-box': string;
            'light-speed-in-left': string;
            'light-speed-in-right': string;
            'light-speed-out-left': string;
            'light-speed-out-right': string;
            flip: string;
            'flip-in-x': string;
            'flip-in-y': string;
            'flip-out-x': string;
            'flip-out-y': string;
            'rotate-in': string;
            'rotate-in-down-left': string;
            'rotate-in-down-right': string;
            'rotate-in-up-left': string;
            'rotate-in-up-right': string;
            'rotate-out': string;
            'rotate-out-down-left': string;
            'rotate-out-down-right': string;
            'rotate-out-up-left': string;
            'rotate-out-up-right': string;
            'roll-in': string;
            'roll-out': string;
            'zoom-in': string;
            'zoom-in-down': string;
            'zoom-in-left': string;
            'zoom-in-right': string;
            'zoom-in-up': string;
            'zoom-out': string;
            'zoom-out-down': string;
            'zoom-out-left': string;
            'zoom-out-right': string;
            'zoom-out-up': string;
            'bounce-in': string;
            'bounce-in-down': string;
            'bounce-in-left': string;
            'bounce-in-right': string;
            'bounce-in-up': string;
            'bounce-out': string;
            'bounce-out-down': string;
            'bounce-out-left': string;
            'bounce-out-right': string;
            'bounce-out-up': string;
            'slide-in-down': string;
            'slide-in-left': string;
            'slide-in-right': string;
            'slide-in-up': string;
            'slide-out-down': string;
            'slide-out-left': string;
            'slide-out-right': string;
            'slide-out-up': string;
            'fade-in': string;
            'fade-in-down': string;
            'fade-in-down-big': string;
            'fade-in-left': string;
            'fade-in-left-big': string;
            'fade-in-right': string;
            'fade-in-right-big': string;
            'fade-in-up': string;
            'fade-in-up-big': string;
            'fade-in-top-left': string;
            'fade-in-top-right': string;
            'fade-in-bottom-left': string;
            'fade-in-bottom-right': string;
            'fade-out': string;
            'fade-out-down': string;
            'fade-out-down-big': string;
            'fade-out-left': string;
            'fade-out-left-big': string;
            'fade-out-right': string;
            'fade-out-right-big': string;
            'fade-out-up': string;
            'fade-out-up-big': string;
            'fade-out-top-left': string;
            'fade-out-top-right': string;
            'fade-out-bottom-left': string;
            'fade-out-bottom-right': string;
            'back-in-up': string;
            'back-in-down': string;
            'back-in-right': string;
            'back-in-left': string;
            'back-out-up': string;
            'back-out-down': string;
            'back-out-right': string;
            'back-out-left': string;
        };
        durations: {
            pulse: string;
            'heart-beat': string;
            'bounce-in': string;
            'bounce-out': string;
            'flip-out-x': string;
            'flip-out-y': string;
            hinge: string;
        };
        timingFns: {
            pulse: string;
            ping: string;
            'head-shake': string;
            'heart-beat': string;
            'pulse-alt': string;
            'light-speed-in-left': string;
            'light-speed-in-right': string;
            'light-speed-out-left': string;
            'light-speed-out-right': string;
        };
        properties: {
            'bounce-alt': {
                'transform-origin': string;
            };
            jello: {
                'transform-origin': string;
            };
            swing: {
                'transform-origin': string;
            };
            flip: {
                'backface-visibility': string;
            };
            'flip-in-x': {
                'backface-visibility': string;
            };
            'flip-in-y': {
                'backface-visibility': string;
            };
            'flip-out-x': {
                'backface-visibility': string;
            };
            'flip-out-y': {
                'backface-visibility': string;
            };
            'rotate-in': {
                'transform-origin': string;
            };
            'rotate-in-down-left': {
                'transform-origin': string;
            };
            'rotate-in-down-right': {
                'transform-origin': string;
            };
            'rotate-in-up-left': {
                'transform-origin': string;
            };
            'rotate-in-up-right': {
                'transform-origin': string;
            };
            'rotate-out': {
                'transform-origin': string;
            };
            'rotate-out-down-left': {
                'transform-origin': string;
            };
            'rotate-out-down-right': {
                'transform-origin': string;
            };
            'rotate-out-up-left': {
                'transform-origin': string;
            };
            'rotate-out-up-right': {
                'transform-origin': string;
            };
            hinge: {
                'transform-origin': string;
            };
            'zoom-out-down': {
                'transform-origin': string;
            };
            'zoom-out-left': {
                'transform-origin': string;
            };
            'zoom-out-right': {
                'transform-origin': string;
            };
            'zoom-out-up': {
                'transform-origin': string;
            };
        };
        counts: {
            spin: string;
            ping: string;
            pulse: string;
            'pulse-alt': string;
            bounce: string;
            'bounce-alt': string;
        };
        category: {
            pulse: string;
            bounce: string;
            spin: string;
            ping: string;
            'bounce-alt': string;
            flash: string;
            'pulse-alt': string;
            'rubber-band': string;
            'shake-x': string;
            'shake-y': string;
            'head-shake': string;
            swing: string;
            tada: string;
            wobble: string;
            jello: string;
            'heart-beat': string;
            hinge: string;
            'jack-in-the-box': string;
            'light-speed-in-left': string;
            'light-speed-in-right': string;
            'light-speed-out-left': string;
            'light-speed-out-right': string;
            flip: string;
            'flip-in-x': string;
            'flip-in-y': string;
            'flip-out-x': string;
            'flip-out-y': string;
            'rotate-in': string;
            'rotate-in-down-left': string;
            'rotate-in-down-right': string;
            'rotate-in-up-left': string;
            'rotate-in-up-right': string;
            'rotate-out': string;
            'rotate-out-down-left': string;
            'rotate-out-down-right': string;
            'rotate-out-up-left': string;
            'rotate-out-up-right': string;
            'roll-in': string;
            'roll-out': string;
            'zoom-in': string;
            'zoom-in-down': string;
            'zoom-in-left': string;
            'zoom-in-right': string;
            'zoom-in-up': string;
            'zoom-out': string;
            'zoom-out-down': string;
            'zoom-out-left': string;
            'zoom-out-right': string;
            'zoom-out-up': string;
            'bounce-in': string;
            'bounce-in-down': string;
            'bounce-in-left': string;
            'bounce-in-right': string;
            'bounce-in-up': string;
            'bounce-out': string;
            'bounce-out-down': string;
            'bounce-out-left': string;
            'bounce-out-right': string;
            'bounce-out-up': string;
            'slide-in-down': string;
            'slide-in-left': string;
            'slide-in-right': string;
            'slide-in-up': string;
            'slide-out-down': string;
            'slide-out-left': string;
            'slide-out-right': string;
            'slide-out-up': string;
            'fade-in': string;
            'fade-in-down': string;
            'fade-in-down-big': string;
            'fade-in-left': string;
            'fade-in-left-big': string;
            'fade-in-right': string;
            'fade-in-right-big': string;
            'fade-in-up': string;
            'fade-in-up-big': string;
            'fade-in-top-left': string;
            'fade-in-top-right': string;
            'fade-in-bottom-left': string;
            'fade-in-bottom-right': string;
            'fade-out': string;
            'fade-out-down': string;
            'fade-out-down-big': string;
            'fade-out-left': string;
            'fade-out-left-big': string;
            'fade-out-right': string;
            'fade-out-right-big': string;
            'fade-out-up': string;
            'fade-out-up-big': string;
            'fade-out-top-left': string;
            'fade-out-top-right': string;
            'fade-out-bottom-left': string;
            'fade-out-bottom-right': string;
            'back-in-up': string;
            'back-in-down': string;
            'back-in-right': string;
            'back-in-left': string;
            'back-out-up': string;
            'back-out-down': string;
            'back-out-right': string;
            'back-out-left': string;
        };
    };
    blur: {
        DEFAULT: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
    };
    perspective: {
        dramatic: string;
        near: string;
        normal: string;
        midrange: string;
        distant: string;
    };
    property: {
        none: string;
        all: string;
        colors: string;
        opacity: string;
        shadow: string;
        transform: string;
        readonly DEFAULT: string;
    };
    default: {
        transition: {
            duration: string;
            timingFunction: string;
        };
        font: {
            family: string;
            featureSettings: string;
            variationSettings: string;
        };
        monoFont: {
            family: string;
            featureSettings: string;
            variationSettings: string;
        };
    };
    container: {
        '3xs': string;
        '2xs': string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
        '4xl': string;
        '5xl': string;
        '6xl': string;
        '7xl': string;
        prose: string;
    };
    aria: {
        busy: string;
        checked: string;
        disabled: string;
        expanded: string;
        hidden: string;
        pressed: string;
        readonly: string;
        required: string;
        selected: string;
    };
    media: {
        portrait: string;
        landscape: string;
        os_dark: string;
        os_light: string;
        motion_ok: string;
        motion_not_ok: string;
        high_contrast: string;
        low_contrast: string;
        opacity_ok: string;
        opacity_not_ok: string;
        use_data_ok: string;
        use_data_not_ok: string;
        touch: string;
        stylus: string;
        pointer: string;
        mouse: string;
        hd_color: string;
    };
    supports: {
        grid: string;
    };
};

export { theme as t };
