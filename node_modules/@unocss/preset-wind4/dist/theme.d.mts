export { colors } from './colors.mjs';
export { t as theme } from './shared/preset-wind4.VVeg6taI.mjs';
import { T as Theme } from './shared/preset-wind4.CeNzqhCV.mjs';
export { C as Colors, a as ThemeAnimation } from './shared/preset-wind4.CeNzqhCV.mjs';

declare const animation: {
    keyframes: {
        pulse: string;
        bounce: string;
        spin: string;
        ping: string;
        'bounce-alt': string;
        flash: string;
        'pulse-alt': string;
        'rubber-band': string;
        'shake-x': string;
        'shake-y': string;
        'head-shake': string;
        swing: string;
        tada: string;
        wobble: string;
        jello: string;
        'heart-beat': string;
        hinge: string;
        'jack-in-the-box': string;
        'light-speed-in-left': string;
        'light-speed-in-right': string;
        'light-speed-out-left': string;
        'light-speed-out-right': string;
        flip: string;
        'flip-in-x': string;
        'flip-in-y': string;
        'flip-out-x': string;
        'flip-out-y': string;
        'rotate-in': string;
        'rotate-in-down-left': string;
        'rotate-in-down-right': string;
        'rotate-in-up-left': string;
        'rotate-in-up-right': string;
        'rotate-out': string;
        'rotate-out-down-left': string;
        'rotate-out-down-right': string;
        'rotate-out-up-left': string;
        'rotate-out-up-right': string;
        'roll-in': string;
        'roll-out': string;
        'zoom-in': string;
        'zoom-in-down': string;
        'zoom-in-left': string;
        'zoom-in-right': string;
        'zoom-in-up': string;
        'zoom-out': string;
        'zoom-out-down': string;
        'zoom-out-left': string;
        'zoom-out-right': string;
        'zoom-out-up': string;
        'bounce-in': string;
        'bounce-in-down': string;
        'bounce-in-left': string;
        'bounce-in-right': string;
        'bounce-in-up': string;
        'bounce-out': string;
        'bounce-out-down': string;
        'bounce-out-left': string;
        'bounce-out-right': string;
        'bounce-out-up': string;
        'slide-in-down': string;
        'slide-in-left': string;
        'slide-in-right': string;
        'slide-in-up': string;
        'slide-out-down': string;
        'slide-out-left': string;
        'slide-out-right': string;
        'slide-out-up': string;
        'fade-in': string;
        'fade-in-down': string;
        'fade-in-down-big': string;
        'fade-in-left': string;
        'fade-in-left-big': string;
        'fade-in-right': string;
        'fade-in-right-big': string;
        'fade-in-up': string;
        'fade-in-up-big': string;
        'fade-in-top-left': string;
        'fade-in-top-right': string;
        'fade-in-bottom-left': string;
        'fade-in-bottom-right': string;
        'fade-out': string;
        'fade-out-down': string;
        'fade-out-down-big': string;
        'fade-out-left': string;
        'fade-out-left-big': string;
        'fade-out-right': string;
        'fade-out-right-big': string;
        'fade-out-up': string;
        'fade-out-up-big': string;
        'fade-out-top-left': string;
        'fade-out-top-right': string;
        'fade-out-bottom-left': string;
        'fade-out-bottom-right': string;
        'back-in-up': string;
        'back-in-down': string;
        'back-in-right': string;
        'back-in-left': string;
        'back-out-up': string;
        'back-out-down': string;
        'back-out-right': string;
        'back-out-left': string;
    };
    durations: {
        pulse: string;
        'heart-beat': string;
        'bounce-in': string;
        'bounce-out': string;
        'flip-out-x': string;
        'flip-out-y': string;
        hinge: string;
    };
    timingFns: {
        pulse: string;
        ping: string;
        'head-shake': string;
        'heart-beat': string;
        'pulse-alt': string;
        'light-speed-in-left': string;
        'light-speed-in-right': string;
        'light-speed-out-left': string;
        'light-speed-out-right': string;
    };
    properties: {
        'bounce-alt': {
            'transform-origin': string;
        };
        jello: {
            'transform-origin': string;
        };
        swing: {
            'transform-origin': string;
        };
        flip: {
            'backface-visibility': string;
        };
        'flip-in-x': {
            'backface-visibility': string;
        };
        'flip-in-y': {
            'backface-visibility': string;
        };
        'flip-out-x': {
            'backface-visibility': string;
        };
        'flip-out-y': {
            'backface-visibility': string;
        };
        'rotate-in': {
            'transform-origin': string;
        };
        'rotate-in-down-left': {
            'transform-origin': string;
        };
        'rotate-in-down-right': {
            'transform-origin': string;
        };
        'rotate-in-up-left': {
            'transform-origin': string;
        };
        'rotate-in-up-right': {
            'transform-origin': string;
        };
        'rotate-out': {
            'transform-origin': string;
        };
        'rotate-out-down-left': {
            'transform-origin': string;
        };
        'rotate-out-down-right': {
            'transform-origin': string;
        };
        'rotate-out-up-left': {
            'transform-origin': string;
        };
        'rotate-out-up-right': {
            'transform-origin': string;
        };
        hinge: {
            'transform-origin': string;
        };
        'zoom-out-down': {
            'transform-origin': string;
        };
        'zoom-out-left': {
            'transform-origin': string;
        };
        'zoom-out-right': {
            'transform-origin': string;
        };
        'zoom-out-up': {
            'transform-origin': string;
        };
    };
    counts: {
        spin: string;
        ping: string;
        pulse: string;
        'pulse-alt': string;
        bounce: string;
        'bounce-alt': string;
    };
    category: {
        pulse: string;
        bounce: string;
        spin: string;
        ping: string;
        'bounce-alt': string;
        flash: string;
        'pulse-alt': string;
        'rubber-band': string;
        'shake-x': string;
        'shake-y': string;
        'head-shake': string;
        swing: string;
        tada: string;
        wobble: string;
        jello: string;
        'heart-beat': string;
        hinge: string;
        'jack-in-the-box': string;
        'light-speed-in-left': string;
        'light-speed-in-right': string;
        'light-speed-out-left': string;
        'light-speed-out-right': string;
        flip: string;
        'flip-in-x': string;
        'flip-in-y': string;
        'flip-out-x': string;
        'flip-out-y': string;
        'rotate-in': string;
        'rotate-in-down-left': string;
        'rotate-in-down-right': string;
        'rotate-in-up-left': string;
        'rotate-in-up-right': string;
        'rotate-out': string;
        'rotate-out-down-left': string;
        'rotate-out-down-right': string;
        'rotate-out-up-left': string;
        'rotate-out-up-right': string;
        'roll-in': string;
        'roll-out': string;
        'zoom-in': string;
        'zoom-in-down': string;
        'zoom-in-left': string;
        'zoom-in-right': string;
        'zoom-in-up': string;
        'zoom-out': string;
        'zoom-out-down': string;
        'zoom-out-left': string;
        'zoom-out-right': string;
        'zoom-out-up': string;
        'bounce-in': string;
        'bounce-in-down': string;
        'bounce-in-left': string;
        'bounce-in-right': string;
        'bounce-in-up': string;
        'bounce-out': string;
        'bounce-out-down': string;
        'bounce-out-left': string;
        'bounce-out-right': string;
        'bounce-out-up': string;
        'slide-in-down': string;
        'slide-in-left': string;
        'slide-in-right': string;
        'slide-in-up': string;
        'slide-out-down': string;
        'slide-out-left': string;
        'slide-out-right': string;
        'slide-out-up': string;
        'fade-in': string;
        'fade-in-down': string;
        'fade-in-down-big': string;
        'fade-in-left': string;
        'fade-in-left-big': string;
        'fade-in-right': string;
        'fade-in-right-big': string;
        'fade-in-up': string;
        'fade-in-up-big': string;
        'fade-in-top-left': string;
        'fade-in-top-right': string;
        'fade-in-bottom-left': string;
        'fade-in-bottom-right': string;
        'fade-out': string;
        'fade-out-down': string;
        'fade-out-down-big': string;
        'fade-out-left': string;
        'fade-out-left-big': string;
        'fade-out-right': string;
        'fade-out-right-big': string;
        'fade-out-up': string;
        'fade-out-up-big': string;
        'fade-out-top-left': string;
        'fade-out-top-right': string;
        'fade-out-bottom-left': string;
        'fade-out-bottom-right': string;
        'back-in-up': string;
        'back-in-down': string;
        'back-in-right': string;
        'back-in-left': string;
        'back-out-up': string;
        'back-out-down': string;
        'back-out-right': string;
        'back-out-left': string;
    };
};

declare const aria: {
    busy: string;
    checked: string;
    disabled: string;
    expanded: string;
    hidden: string;
    pressed: string;
    readonly: string;
    required: string;
    selected: string;
};

declare const blur: {
    DEFAULT: string;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
};

declare const font: {
    sans: string;
    serif: string;
    mono: string;
};
declare const text: {
    xs: {
        fontSize: string;
        lineHeight: string;
    };
    sm: {
        fontSize: string;
        lineHeight: string;
    };
    base: {
        fontSize: string;
        lineHeight: string;
    };
    lg: {
        fontSize: string;
        lineHeight: string;
    };
    xl: {
        fontSize: string;
        lineHeight: string;
    };
    '2xl': {
        fontSize: string;
        lineHeight: string;
    };
    '3xl': {
        fontSize: string;
        lineHeight: string;
    };
    '4xl': {
        fontSize: string;
        lineHeight: string;
    };
    '5xl': {
        fontSize: string;
        lineHeight: string;
    };
    '6xl': {
        fontSize: string;
        lineHeight: string;
    };
    '7xl': {
        fontSize: string;
        lineHeight: string;
    };
    '8xl': {
        fontSize: string;
        lineHeight: string;
    };
    '9xl': {
        fontSize: string;
        lineHeight: string;
    };
};
declare const fontWeight: {
    thin: string;
    extralight: string;
    light: string;
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
    extrabold: string;
    black: string;
};
declare const tracking: {
    tighter: string;
    tight: string;
    normal: string;
    wide: string;
    wider: string;
    widest: string;
};
declare const leading: {
    none: string;
    tight: string;
    snug: string;
    normal: string;
    relaxed: string;
    loose: string;
};
declare const textStrokeWidth: Theme['textStrokeWidth'];

declare const media: {
    portrait: string;
    landscape: string;
    os_dark: string;
    os_light: string;
    motion_ok: string;
    motion_not_ok: string;
    high_contrast: string;
    low_contrast: string;
    opacity_ok: string;
    opacity_not_ok: string;
    use_data_ok: string;
    use_data_not_ok: string;
    touch: string;
    stylus: string;
    pointer: string;
    mouse: string;
    hd_color: string;
};

declare const spacing: {
    DEFAULT: string;
    xs: string;
    sm: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
    '7xl': string;
    '8xl': string;
    '9xl': string;
};
declare const radius: {
    DEFAULT: string;
    none: string;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
};
declare const shadow: {
    DEFAULT: string[];
    '2xs': string;
    xs: string;
    sm: string[];
    md: string[];
    lg: string[];
    xl: string[];
    '2xl': string;
    none: string;
    inner: string;
};
declare const insetShadow: {
    '2xs': string;
    xs: string;
    sm: string;
    none: string;
};
declare const dropShadow: {
    DEFAULT: string[];
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
};
declare const textShadow: {
    none: string;
    '2xs': string;
    xs: string;
    sm: string[];
    md: string[];
    lg: string[];
};
declare const perspective: {
    dramatic: string;
    near: string;
    normal: string;
    midrange: string;
    distant: string;
};
/** For reset css */
declare const defaults: {
    transition: {
        duration: string;
        timingFunction: string;
    };
    font: {
        family: string;
        featureSettings: string;
        variationSettings: string;
    };
    monoFont: {
        family: string;
        featureSettings: string;
        variationSettings: string;
    };
};

declare const container: {
    '3xs': string;
    '2xs': string;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    '5xl': string;
    '6xl': string;
    '7xl': string;
    prose: string;
};
declare const breakpoint: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
};
declare const verticalBreakpoint: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
};

declare const supports: {
    grid: string;
};

declare const ease: {
    linear: string;
    in: string;
    out: string;
    'in-out': string;
    DEFAULT: string;
};
declare const property: {
    none: string;
    all: string;
    colors: string;
    opacity: string;
    shadow: string;
    transform: string;
    readonly DEFAULT: string;
};

export { Theme, animation, aria, blur, breakpoint, container, defaults, dropShadow, ease, font, fontWeight, insetShadow, leading, media, perspective, property, radius, shadow, spacing, supports, text, textShadow, textStrokeWidth, tracking, verticalBreakpoint };
