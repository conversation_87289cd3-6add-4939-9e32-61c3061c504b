export { F as placeholderModifier, v as variantAria, b as variantBreakpoints, c as variantChildren, g as variantColorsMediaOrClass, h as variantColorsScheme, d as variantCombinators, f as variantContainerQuery, r as variantContrasts, x as variantCssLayer, q as variantCustomMedia, i as variantDataAttribute, u as variantForcedColors, D as variantImplicitGroup, m as variantImportant, y as variantInternalLayer, l as variantLanguageDirections, s as variantMotions, E as variantNegative, n as variantNoscript, t as variantOrientations, J as variantPartClasses, p as variantPrint, H as variantPseudoClassFunctions, G as variantPseudoClassesAndElements, z as variantScope, o as variantScripting, w as variantSelector, K as variantStartingStyle, C as variantStickyHover, L as variantSupports, e as variantSvgCombinators, a as variantTaggedAriaAttributes, j as variantTaggedDataAttributes, I as variantTaggedPseudoClasses, B as variantTheme, A as variantVariables, k as variants } from './shared/preset-wind4.CnuVK22X.mjs';
import '@unocss/rule-utils';
import './shared/preset-wind4.DQ5mCnzs.mjs';
import '@unocss/core';
