import { VariantObject, Variant, VariantFunction } from '@unocss/core';
import { T as Theme } from './shared/preset-wind4.CeNzqhCV.js';
import { PresetWind4Options } from './index.js';
export { variants } from './index.js';
import './shared/preset-wind4.oLaz9HAb.js';
import './shortcuts.js';
import './shared/preset-wind4.VVeg6taI.js';

declare const variantAria: VariantObject<Theme>;
declare const variantTaggedAriaAttributes: Variant<Theme>[];

declare function variantBreakpoints(): VariantObject<Theme>;

declare const variantChildren: Variant<Theme>[];

declare const variantCombinators: Variant<Theme>[];
declare const variantSvgCombinators: Variant<Theme>[];

declare const variantContainerQuery: VariantObject<Theme>;

declare function variantColorsMediaOrClass(options?: PresetWind4Options): Variant<Theme>[];
declare const variantColorsScheme: Variant<Theme>[];

declare const variantDataAttribute: VariantObject<Theme>;
declare const variantTaggedDataAttributes: Variant<Theme>[];

declare const variantLanguageDirections: Variant<Theme>[];

declare function variantImportant(): VariantObject<Theme>;

declare const variantNoscript: VariantObject;
declare const variantScripting: VariantObject<Theme>;
declare const variantPrint: VariantObject<Theme>;
declare const variantCustomMedia: VariantObject<Theme>;
declare const variantContrasts: Variant<Theme>[];
declare const variantMotions: Variant<Theme>[];
declare const variantOrientations: Variant<Theme>[];
declare const variantForcedColors: Variant<Theme>[];

declare const variantSelector: Variant<Theme>;
declare const variantCssLayer: Variant<Theme>;
declare const variantInternalLayer: Variant<Theme>;
declare const variantScope: Variant<Theme>;
declare const variantVariables: Variant<Theme>;
declare const variantTheme: Variant<Theme>;
declare const variantStickyHover: Variant<Theme>[];
declare const variantImplicitGroup: Variant;

declare const variantNegative: Variant<Theme>;

declare const placeholderModifier: VariantFunction<Theme>;

declare function variantPseudoClassesAndElements(): VariantObject<Theme>[];
declare function variantPseudoClassFunctions(): VariantObject<Theme>;
declare function variantTaggedPseudoClasses(options?: PresetWind4Options): VariantObject<Theme>[];
declare const variantPartClasses: VariantObject<Theme>;

declare const variantStartingStyle: Variant<Theme>;

declare const variantSupports: VariantObject<Theme>;

export { placeholderModifier, variantAria, variantBreakpoints, variantChildren, variantColorsMediaOrClass, variantColorsScheme, variantCombinators, variantContainerQuery, variantContrasts, variantCssLayer, variantCustomMedia, variantDataAttribute, variantForcedColors, variantImplicitGroup, variantImportant, variantInternalLayer, variantLanguageDirections, variantMotions, variantNegative, variantNoscript, variantOrientations, variantPartClasses, variantPrint, variantPseudoClassFunctions, variantPseudoClassesAndElements, variantScope, variantScripting, variantSelector, variantStartingStyle, variantStickyHover, variantSupports, variantSvgCombinators, variantTaggedAriaAttributes, variantTaggedDataAttributes, variantTaggedPseudoClasses, variantTheme, variantVariables };
