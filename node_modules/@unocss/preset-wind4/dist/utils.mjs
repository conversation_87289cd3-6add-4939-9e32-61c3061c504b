import { a as remRE } from './shared/preset-wind4.DQ5mCnzs.mjs';
export { C as CONTROL_NO_NEGATIVE, P as PRESET_NAME, S as SpecialColorKey, K as camelize, A as colorCSSGenerator, B as colorResolver, D as colorableShadows, M as compressCSS, c as cornerMap, f as cssMathFnRE, j as cssVarFnRE, H as defineProperty, m as detectThemeValue, d as directionMap, s as directionSize, l as generateThemeVariable, z as getThemeByKey, g as globalKeywords, b as h, h as handler, E as hasParseableColor, L as hyphenate, i as insetMap, I as isCSSMathFn, J as isSize, G as makeGlobalStaticRules, q as numberResolver, w as parseColor, y as parseThemeColor, p as positionMap, o as propertyTracking, r as resolveBreakpoints, F as resolveVerticalBreakpoints, u as splitShorthand, k as themeTracking, n as trackedProperties, t as trackedTheme, v as valueHandlers, e as xyzArray, x as xyzMap } from './shared/preset-wind4.DQ5mCnzs.mjs';
export * from '@unocss/rule-utils';
import '@unocss/core';

function createRemToPxProcessor(base = 16) {
  function resolver(utility) {
    if (typeof utility[1] === "string" && remRE.test(utility[1]))
      utility[1] = utility[1].replace(remRE, (_, p1) => `${p1 * base}px`);
  }
  return (utilObjectOrEntry) => {
    if (Array.isArray(utilObjectOrEntry)) {
      resolver(utilObjectOrEntry);
    } else {
      utilObjectOrEntry.entries.forEach(resolver);
    }
  };
}

export { createRemToPxProcessor };
