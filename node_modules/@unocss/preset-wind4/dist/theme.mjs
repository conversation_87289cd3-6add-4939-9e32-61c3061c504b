import { colors } from './colors.mjs';

const animation = {
  keyframes: {
    "pulse": "{0%, 100% {opacity:1} 50% {opacity:.5}}",
    "bounce": "{0%, 100% {transform:translateY(-25%);animation-timing-function:cubic-bezier(0.8,0,1,1)} 50% {transform:translateY(0);animation-timing-function:cubic-bezier(0,0,0.2,1)}}",
    "spin": "{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}",
    "ping": "{0%{transform:scale(1);opacity:1}75%,100%{transform:scale(2);opacity:0}}",
    "bounce-alt": "{from,20%,53%,80%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1);transform:translate3d(0,0,0)}40%,43%{animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);transform:translate3d(0,-30px,0)}70%{animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);transform:translate3d(0,-15px,0)}90%{transform:translate3d(0,-4px,0)}}",
    "flash": "{from,50%,to{opacity:1}25%,75%{opacity:0}}",
    "pulse-alt": "{from{transform:scale3d(1,1,1)}50%{transform:scale3d(1.05,1.05,1.05)}to{transform:scale3d(1,1,1)}}",
    "rubber-band": "{from{transform:scale3d(1,1,1)}30%{transform:scale3d(1.25,0.75,1)}40%{transform:scale3d(0.75,1.25,1)}50%{transform:scale3d(1.15,0.85,1)}65%{transform:scale3d(0.95,1.05,1)}75%{transform:scale3d(1.05,0.95,1)}to{transform:scale3d(1,1,1)}}",
    "shake-x": "{from,to{transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{transform:translate3d(-10px,0,0)}20%,40%,60%,80%{transform:translate3d(10px,0,0)}}",
    "shake-y": "{from,to{transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{transform:translate3d(0,-10px,0)}20%,40%,60%,80%{transform:translate3d(0,10px,0)}}",
    "head-shake": "{0%{transform:translateX(0)}6.5%{transform:translateX(-6px) rotateY(-9deg)}18.5%{transform:translateX(5px) rotateY(7deg)}31.5%{transform:translateX(-3px) rotateY(-5deg)}43.5%{transform:translateX(2px) rotateY(3deg)}50%{transform:translateX(0)}}",
    "swing": "{20%{transform:rotate3d(0,0,1,15deg)}40%{transform:rotate3d(0,0,1,-10deg)}60%{transform:rotate3d(0,0,1,5deg)}80%{transform:rotate3d(0,0,1,-5deg)}to{transform:rotate3d(0,0,1,0deg)}}",
    "tada": "{from{transform:scale3d(1,1,1)}10%,20%{transform:scale3d(0.9,0.9,0.9) rotate3d(0,0,1,-3deg)}30%,50%,70%,90%{transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)}40%,60%,80%{transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg)}to{transform:scale3d(1,1,1)}}",
    "wobble": "{from{transform:translate3d(0,0,0)}15%{transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg)}30%{transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg)}45%{transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg)}60%{transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg)}75%{transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg)}to{transform:translate3d(0,0,0)}}",
    "jello": "{from,11.1%,to{transform:translate3d(0,0,0)}22.2%{transform:skewX(-12.5deg) skewY(-12.5deg)}33.3%{transform:skewX(6.25deg) skewY(6.25deg)}44.4%{transform:skewX(-3.125deg)skewY(-3.125deg)}55.5%{transform:skewX(1.5625deg) skewY(1.5625deg)}66.6%{transform:skewX(-0.78125deg) skewY(-0.78125deg)}77.7%{transform:skewX(0.390625deg) skewY(0.390625deg)}88.8%{transform:skewX(-0.1953125deg) skewY(-0.1953125deg)}}",
    "heart-beat": "{0%{transform:scale(1)}14%{transform:scale(1.3)}28%{transform:scale(1)}42%{transform:scale(1.3)}70%{transform:scale(1)}}",
    "hinge": "{0%{transform-origin:top left;animation-timing-function:ease-in-out}20%,60%{transform:rotate3d(0,0,1,80deg);transform-origin:top left;animation-timing-function:ease-in-out}40%,80%{transform:rotate3d(0,0,1,60deg);transform-origin:top left;animation-timing-function:ease-in-out}to{transform:translate3d(0,700px,0);opacity:0}}",
    "jack-in-the-box": "{from{opacity:0;transform-origin:center bottom;transform:scale(0.1) rotate(30deg)}50%{transform:rotate(-10deg)}70%{transform:rotate(3deg)}to{transform:scale(1)}}",
    "light-speed-in-left": "{from{opacity:0;transform:translate3d(-100%,0,0) skewX(-30deg)}60%{opacity:1;transform:skewX(20deg)}80%{transform:skewX(-5deg)}to{transform:translate3d(0,0,0)}}",
    "light-speed-in-right": "{from{opacity:0;transform:translate3d(100%,0,0) skewX(-30deg)}60%{opacity:1;transform:skewX(20deg)}80%{transform:skewX(-5deg)}to{transform:translate3d(0,0,0)}}",
    "light-speed-out-left": "{from{opacity:1}to{opacity:0;transform:translate3d(-100%,0,0) skewX(30deg)}}",
    "light-speed-out-right": "{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0) skewX(30deg)}}",
    "flip": "{from{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,0) rotate3d(0,1,0,-360deg);animation-timing-function:ease-out}40%{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);animation-timing-function:ease-out}50%{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);animation-timing-function:ease-in}80%{transform:perspective(400px) scale3d(0.95,0.95,0.95) translate3d(0,0,0) rotate3d(0,1,0,0deg);animation-timing-function:ease-in}to{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,0) rotate3d(0,1,0,0deg);animation-timing-function:ease-in}}",
    "flip-in-x": "{from{transform:perspective(400px) rotate3d(1,0,0,90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(1,0,0,-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(1,0,0,10deg);opacity:1}80%{transform:perspective(400px) rotate3d(1,0,0,-5deg)}to{transform:perspective(400px)}}",
    "flip-in-y": "{from{transform:perspective(400px) rotate3d(0,1,0,90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(0,1,0,-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(0,1,0,10deg);opacity:1}80%{transform:perspective(400px) rotate3d(0,1,0,-5deg)}to{transform:perspective(400px)}}",
    "flip-out-x": "{from{transform:perspective(400px)}30%{transform:perspective(400px) rotate3d(1,0,0,-20deg);opacity:1}to{transform:perspective(400px) rotate3d(1,0,0,90deg);opacity:0}}",
    "flip-out-y": "{from{transform:perspective(400px)}30%{transform:perspective(400px) rotate3d(0,1,0,-15deg);opacity:1}to{transform:perspective(400px) rotate3d(0,1,0,90deg);opacity:0}}",
    "rotate-in": "{from{transform-origin:center;transform:rotate3d(0,0,1,-200deg);opacity:0}to{transform-origin:center;transform:translate3d(0,0,0);opacity:1}}",
    "rotate-in-down-left": "{from{transform-origin:left bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}to{transform-origin:left bottom;transform:translate3d(0,0,0);opacity:1}}",
    "rotate-in-down-right": "{from{transform-origin:right bottom;transform:rotate3d(0,0,1,45deg);opacity:0}to{transform-origin:right bottom;transform:translate3d(0,0,0);opacity:1}}",
    "rotate-in-up-left": "{from{transform-origin:left top;transform:rotate3d(0,0,1,45deg);opacity:0}to{transform-origin:left top;transform:translate3d(0,0,0);opacity:1}}",
    "rotate-in-up-right": "{from{transform-origin:right bottom;transform:rotate3d(0,0,1,-90deg);opacity:0}to{transform-origin:right bottom;transform:translate3d(0,0,0);opacity:1}}",
    "rotate-out": "{from{transform-origin:center;opacity:1}to{transform-origin:center;transform:rotate3d(0,0,1,200deg);opacity:0}}",
    "rotate-out-down-left": "{from{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,45deg);opacity:0}}",
    "rotate-out-down-right": "{from{transform-origin:right bottom;opacity:1}to{transform-origin:right bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}}",
    "rotate-out-up-left": "{from{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}}",
    "rotate-out-up-right": "{from{transform-origin:right bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,90deg);opacity:0}}",
    "roll-in": "{from{opacity:0;transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "roll-out": "{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)}}",
    "zoom-in": "{from{opacity:0;transform:scale3d(0.3,0.3,0.3)}50%{opacity:1}}",
    "zoom-in-down": "{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,-1000px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}",
    "zoom-in-left": "{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(-1000px,0,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(10px,0,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}",
    "zoom-in-right": "{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(1000px,0,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(-10px,0,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}",
    "zoom-in-up": "{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,1000px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}",
    "zoom-out": "{from{opacity:1}50%{opacity:0;transform:scale3d(0.3,0.3,0.3)}to{opacity:0}}",
    "zoom-out-down": "{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}to{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}",
    "zoom-out-left": "{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(42px,0,0)}to{opacity:0;transform:scale(0.1) translate3d(-2000px,0,0);transform-origin:left center}}",
    "zoom-out-right": "{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(-42px,0,0)}to{opacity:0;transform:scale(0.1) translate3d(2000px,0,0);transform-origin:right center}}",
    "zoom-out-up": "{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}to{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,-2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}",
    "bounce-in": "{from,20%,40%,60%,80%,to{animation-timing-function:ease-in-out}0%{opacity:0;transform:scale3d(0.3,0.3,0.3)}20%{transform:scale3d(1.1,1.1,1.1)}40%{transform:scale3d(0.9,0.9,0.9)}60%{transform:scale3d(1.03,1.03,1.03);opacity:1}80%{transform:scale3d(0.97,0.97,0.97)}to{opacity:1;transform:scale3d(1,1,1)}}",
    "bounce-in-down": "{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:translate3d(0,0,0)}}",
    "bounce-in-left": "{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:translate3d(0,0,0)}}",
    "bounce-in-right": "{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:translate3d(0,0,0)}}",
    "bounce-in-up": "{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translate3d(0,0,0)}}",
    "bounce-out": "{20%{transform:scale3d(0.9,0.9,0.9)}50%,55%{opacity:1;transform:scale3d(1.1,1.1,1.1)}to{opacity:0;transform:scale3d(0.3,0.3,0.3)}}",
    "bounce-out-down": "{20%{transform:translate3d(0,10px,0)}40%,45%{opacity:1;transform:translate3d(0,-20px,0)}to{opacity:0;transform:translate3d(0,2000px,0)}}",
    "bounce-out-left": "{20%{opacity:1;transform:translate3d(20px,0,0)}to{opacity:0;transform:translate3d(-2000px,0,0)}}",
    "bounce-out-right": "{20%{opacity:1;transform:translate3d(-20px,0,0)}to{opacity:0;transform:translate3d(2000px,0,0)}}",
    "bounce-out-up": "{20%{transform:translate3d(0,-10px,0)}40%,45%{opacity:1;transform:translate3d(0,20px,0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}",
    "slide-in-down": "{from{transform:translate3d(0,-100%,0);visibility:visible}to{transform:translate3d(0,0,0)}}",
    "slide-in-left": "{from{transform:translate3d(-100%,0,0);visibility:visible}to{transform:translate3d(0,0,0)}}",
    "slide-in-right": "{from{transform:translate3d(100%,0,0);visibility:visible}to{transform:translate3d(0,0,0)}}",
    "slide-in-up": "{from{transform:translate3d(0,100%,0);visibility:visible}to{transform:translate3d(0,0,0)}}",
    "slide-out-down": "{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(0,100%,0)}}",
    "slide-out-left": "{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(-100%,0,0)}}",
    "slide-out-right": "{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(100%,0,0)}}",
    "slide-out-up": "{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(0,-100%,0)}}",
    "fade-in": "{from{opacity:0}to{opacity:1}}",
    "fade-in-down": "{from{opacity:0;transform:translate3d(0,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-down-big": "{from{opacity:0;transform:translate3d(0,-2000px,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-left": "{from{opacity:0;transform:translate3d(-100%,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-left-big": "{from{opacity:0;transform:translate3d(-2000px,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-right": "{from{opacity:0;transform:translate3d(100%,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-right-big": "{from{opacity:0;transform:translate3d(2000px,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-up": "{from{opacity:0;transform:translate3d(0,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-up-big": "{from{opacity:0;transform:translate3d(0,2000px,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-top-left": "{from{opacity:0;transform:translate3d(-100%,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-top-right": "{from{opacity:0;transform:translate3d(100%,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-bottom-left": "{from{opacity:0;transform:translate3d(-100%,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-in-bottom-right": "{from{opacity:0;transform:translate3d(100%,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}",
    "fade-out": "{from{opacity:1}to{opacity:0}}",
    "fade-out-down": "{from{opacity:1}to{opacity:0;transform:translate3d(0,100%,0)}}",
    "fade-out-down-big": "{from{opacity:1}to{opacity:0;transform:translate3d(0,2000px,0)}}",
    "fade-out-left": "{from{opacity:1}to{opacity:0;transform:translate3d(-100%,0,0)}}",
    "fade-out-left-big": "{from{opacity:1}to{opacity:0;transform:translate3d(-2000px,0,0)}}",
    "fade-out-right": "{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0)}}",
    "fade-out-right-big": "{from{opacity:1}to{opacity:0;transform:translate3d(2000px,0,0)}}",
    "fade-out-up": "{from{opacity:1}to{opacity:0;transform:translate3d(0,-100%,0)}}",
    "fade-out-up-big": "{from{opacity:1}to{opacity:0;transform:translate3d(0,-2000px,0)}}",
    "fade-out-top-left": "{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(-100%,-100%,0)}}",
    "fade-out-top-right": "{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(100%,-100%,0)}}",
    "fade-out-bottom-left": "{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(-100%,100%,0)}}",
    "fade-out-bottom-right": "{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(100%,100%,0)}}",
    "back-in-up": "{0%{opacity:0.7;transform:translateY(1200px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}",
    "back-in-down": "{0%{opacity:0.7;transform:translateY(-1200px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}",
    "back-in-right": "{0%{opacity:0.7;transform:translateX(2000px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}",
    "back-in-left": "{0%{opacity:0.7;transform:translateX(-2000px) scale(0.7)}80%{opacity:0.7;transform:translateX(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}",
    "back-out-up": "{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateY(-700px) scale(0.7)}}",
    "back-out-down": "{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateY(700px) scale(0.7)}}",
    "back-out-right": "{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateX(2000px) scale(0.7)}}",
    "back-out-left": "{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateX(-2000px) scale(0.7)}100%{opacity:0.7;transform:translateY(-700px) scale(0.7)}}"
  },
  durations: {
    "pulse": "2s",
    "heart-beat": "1.3s",
    "bounce-in": "0.75s",
    "bounce-out": "0.75s",
    "flip-out-x": "0.75s",
    "flip-out-y": "0.75s",
    "hinge": "2s"
  },
  timingFns: {
    "pulse": "cubic-bezier(0.4,0,.6,1)",
    "ping": "cubic-bezier(0,0,.2,1)",
    "head-shake": "ease-in-out",
    "heart-beat": "ease-in-out",
    "pulse-alt": "ease-in-out",
    "light-speed-in-left": "ease-out",
    "light-speed-in-right": "ease-out",
    "light-speed-out-left": "ease-in",
    "light-speed-out-right": "ease-in"
  },
  properties: {
    "bounce-alt": { "transform-origin": "center bottom" },
    "jello": { "transform-origin": "center" },
    "swing": { "transform-origin": "top center" },
    "flip": { "backface-visibility": "visible" },
    "flip-in-x": { "backface-visibility": "visible !important" },
    "flip-in-y": { "backface-visibility": "visible !important" },
    "flip-out-x": { "backface-visibility": "visible !important" },
    "flip-out-y": { "backface-visibility": "visible !important" },
    "rotate-in": { "transform-origin": "center" },
    "rotate-in-down-left": { "transform-origin": "left bottom" },
    "rotate-in-down-right": { "transform-origin": "right bottom" },
    "rotate-in-up-left": { "transform-origin": "left bottom" },
    "rotate-in-up-right": { "transform-origin": "right bottom" },
    "rotate-out": { "transform-origin": "center" },
    "rotate-out-down-left": { "transform-origin": "left bottom" },
    "rotate-out-down-right": { "transform-origin": "right bottom" },
    "rotate-out-up-left": { "transform-origin": "left bottom" },
    "rotate-out-up-right": { "transform-origin": "right bottom" },
    "hinge": { "transform-origin": "top left" },
    "zoom-out-down": { "transform-origin": "center bottom" },
    "zoom-out-left": { "transform-origin": "left center" },
    "zoom-out-right": { "transform-origin": "right center" },
    "zoom-out-up": { "transform-origin": "center bottom" }
  },
  counts: {
    "spin": "infinite",
    "ping": "infinite",
    "pulse": "infinite",
    "pulse-alt": "infinite",
    "bounce": "infinite",
    "bounce-alt": "infinite"
  },
  category: {
    "pulse": "Attention Seekers",
    "bounce": "Attention Seekers",
    "spin": "Attention Seekers",
    "ping": "Attention Seekers",
    "bounce-alt": "Attention Seekers",
    "flash": "Attention Seekers",
    "pulse-alt": "Attention Seekers",
    "rubber-band": "Attention Seekers",
    "shake-x": "Attention Seekers",
    "shake-y": "Attention Seekers",
    "head-shake": "Attention Seekers",
    "swing": "Attention Seekers",
    "tada": "Attention Seekers",
    "wobble": "Attention Seekers",
    "jello": "Attention Seekers",
    "heart-beat": "Attention Seekers",
    "hinge": "Specials",
    "jack-in-the-box": "Specials",
    "light-speed-in-left": "Lightspeed",
    "light-speed-in-right": "Lightspeed",
    "light-speed-out-left": "Lightspeed",
    "light-speed-out-right": "Lightspeed",
    "flip": "Flippers",
    "flip-in-x": "Flippers",
    "flip-in-y": "Flippers",
    "flip-out-x": "Flippers",
    "flip-out-y": "Flippers",
    "rotate-in": "Rotating Entrances",
    "rotate-in-down-left": "Rotating Entrances",
    "rotate-in-down-right": "Rotating Entrances",
    "rotate-in-up-left": "Rotating Entrances",
    "rotate-in-up-right": "Rotating Entrances",
    "rotate-out": "Rotating Exits",
    "rotate-out-down-left": "Rotating Exits",
    "rotate-out-down-right": "Rotating Exits",
    "rotate-out-up-left": "Rotating Exits",
    "rotate-out-up-right": "Rotating Exits",
    "roll-in": "Specials",
    "roll-out": "Specials",
    "zoom-in": "Zooming Entrances",
    "zoom-in-down": "Zooming Entrances",
    "zoom-in-left": "Zooming Entrances",
    "zoom-in-right": "Zooming Entrances",
    "zoom-in-up": "Zooming Entrances",
    "zoom-out": "Zooming Exits",
    "zoom-out-down": "Zooming Exits",
    "zoom-out-left": "Zooming Exits",
    "zoom-out-right": "Zooming Exits",
    "zoom-out-up": "Zooming Exits",
    "bounce-in": "Bouncing Entrances",
    "bounce-in-down": "Bouncing Entrances",
    "bounce-in-left": "Bouncing Entrances",
    "bounce-in-right": "Bouncing Entrances",
    "bounce-in-up": "Bouncing Entrances",
    "bounce-out": "Bouncing Exits",
    "bounce-out-down": "Bouncing Exits",
    "bounce-out-left": "Bouncing Exits",
    "bounce-out-right": "Bouncing Exits",
    "bounce-out-up": "Bouncing Exits",
    "slide-in-down": "Sliding Entrances",
    "slide-in-left": "Sliding Entrances",
    "slide-in-right": "Sliding Entrances",
    "slide-in-up": "Sliding Entrances",
    "slide-out-down": "Sliding Exits",
    "slide-out-left": "Sliding Exits",
    "slide-out-right": "Sliding Exits",
    "slide-out-up": "Sliding Exits",
    "fade-in": "Fading Entrances",
    "fade-in-down": "Fading Entrances",
    "fade-in-down-big": "Fading Entrances",
    "fade-in-left": "Fading Entrances",
    "fade-in-left-big": "Fading Entrances",
    "fade-in-right": "Fading Entrances",
    "fade-in-right-big": "Fading Entrances",
    "fade-in-up": "Fading Entrances",
    "fade-in-up-big": "Fading Entrances",
    "fade-in-top-left": "Fading Entrances",
    "fade-in-top-right": "Fading Entrances",
    "fade-in-bottom-left": "Fading Entrances",
    "fade-in-bottom-right": "Fading Entrances",
    "fade-out": "Fading Exits",
    "fade-out-down": "Fading Exits",
    "fade-out-down-big": "Fading Exits",
    "fade-out-left": "Fading Exits",
    "fade-out-left-big": "Fading Exits",
    "fade-out-right": "Fading Exits",
    "fade-out-right-big": "Fading Exits",
    "fade-out-up": "Fading Exits",
    "fade-out-up-big": "Fading Exits",
    "fade-out-top-left": "Fading Exits",
    "fade-out-top-right": "Fading Exits",
    "fade-out-bottom-left": "Fading Exits",
    "fade-out-bottom-right": "Fading Exits",
    "back-in-up": "Back Entrances",
    "back-in-down": "Back Entrances",
    "back-in-right": "Back Entrances",
    "back-in-left": "Back Entrances",
    "back-out-up": "Back Exits",
    "back-out-down": "Back Exits",
    "back-out-right": "Back Exits",
    "back-out-left": "Back Exits"
  }
};

const aria = {
  busy: 'busy="true"',
  checked: 'checked="true"',
  disabled: 'disabled="true"',
  expanded: 'expanded="true"',
  hidden: 'hidden="true"',
  pressed: 'pressed="true"',
  readonly: 'readonly="true"',
  required: 'required="true"',
  selected: 'selected="true"'
};

const blur = {
  "DEFAULT": "8px",
  "xs": "4px",
  "sm": "8px",
  "md": "12px",
  "lg": "16px",
  "xl": "24px",
  "2xl": "40px",
  "3xl": "64px"
};

const font = {
  sans: [
    "ui-sans-serif",
    "system-ui",
    "-apple-system",
    "BlinkMacSystemFont",
    '"Segoe UI"',
    "Roboto",
    '"Helvetica Neue"',
    "Arial",
    '"Noto Sans"',
    "sans-serif",
    '"Apple Color Emoji"',
    '"Segoe UI Emoji"',
    '"Segoe UI Symbol"',
    '"Noto Color Emoji"'
  ].join(","),
  serif: [
    "ui-serif",
    "Georgia",
    "Cambria",
    '"Times New Roman"',
    "Times",
    "serif"
  ].join(","),
  mono: [
    "ui-monospace",
    "SFMono-Regular",
    "Menlo",
    "Monaco",
    "Consolas",
    '"Liberation Mono"',
    '"Courier New"',
    "monospace"
  ].join(",")
};
const text = {
  "xs": { fontSize: "0.75rem", lineHeight: "1rem" },
  "sm": { fontSize: "0.875rem", lineHeight: "1.25rem" },
  "base": { fontSize: "1rem", lineHeight: "1.5rem" },
  "lg": { fontSize: "1.125rem", lineHeight: "1.75rem" },
  "xl": { fontSize: "1.25rem", lineHeight: "1.75rem" },
  "2xl": { fontSize: "1.5rem", lineHeight: "2rem" },
  "3xl": { fontSize: "1.875rem", lineHeight: "2.25rem" },
  "4xl": { fontSize: "2.25rem", lineHeight: "2.5rem" },
  "5xl": { fontSize: "3rem", lineHeight: "1" },
  "6xl": { fontSize: "3.75rem", lineHeight: "1" },
  "7xl": { fontSize: "4.5rem", lineHeight: "1" },
  "8xl": { fontSize: "6rem", lineHeight: "1" },
  "9xl": { fontSize: "8rem", lineHeight: "1" }
};
const fontWeight = {
  thin: "100",
  extralight: "200",
  light: "300",
  normal: "400",
  medium: "500",
  semibold: "600",
  bold: "700",
  extrabold: "800",
  black: "900"
};
const tracking = {
  tighter: "-0.05em",
  tight: "-0.025em",
  normal: "0em",
  wide: "0.025em",
  wider: "0.05em",
  widest: "0.1em"
};
const leading = {
  none: "1",
  tight: "1.25",
  snug: "1.375",
  normal: "1.5",
  relaxed: "1.625",
  loose: "2"
};
const textStrokeWidth = {
  DEFAULT: "1.5rem",
  none: "0",
  sm: "thin",
  md: "medium",
  lg: "thick"
};

const media = {
  portrait: "(orientation: portrait)",
  landscape: "(orientation: landscape)",
  os_dark: "(prefers-color-scheme: dark)",
  os_light: "(prefers-color-scheme: light)",
  motion_ok: "(prefers-reduced-motion: no-preference)",
  motion_not_ok: "(prefers-reduced-motion: reduce)",
  high_contrast: "(prefers-contrast: high)",
  low_contrast: "(prefers-contrast: low)",
  opacity_ok: "(prefers-reduced-transparency: no-preference)",
  opacity_not_ok: "(prefers-reduced-transparency: reduce)",
  use_data_ok: "(prefers-reduced-data: no-preference)",
  use_data_not_ok: "(prefers-reduced-data: reduce)",
  touch: "(hover: none) and (pointer: coarse)",
  stylus: "(hover: none) and (pointer: fine)",
  pointer: "(hover) and (pointer: coarse)",
  mouse: "(hover) and (pointer: fine)",
  hd_color: "(dynamic-range: high)"
};

const spacing = {
  "DEFAULT": "0.25rem",
  "xs": "0.75rem",
  "sm": "0.875rem",
  "lg": "1.125rem",
  "xl": "1.25rem",
  "2xl": "1.5rem",
  "3xl": "1.875rem",
  "4xl": "2.25rem",
  "5xl": "3rem",
  "6xl": "3.75rem",
  "7xl": "4.5rem",
  "8xl": "6rem",
  "9xl": "8rem"
};
const radius = {
  "DEFAULT": "0.25rem",
  "none": "0",
  "xs": "0.125rem",
  "sm": "0.25rem",
  "md": "0.375rem",
  "lg": "0.5rem",
  "xl": "0.75rem",
  "2xl": "1rem",
  "3xl": "1.5rem",
  "4xl": "2rem"
};
const shadow = {
  "DEFAULT": [`0 1px 3px 0 rgb(0 0 0 / 0.1)`, `0 1px 2px -1px rgb(0 0 0 / 0.1)`],
  "2xs": `0 1px rgb(0 0 0 / 0.05)`,
  "xs": `0 1px 2px 0 rgb(0 0 0 / 0.05)`,
  "sm": [`0 1px 3px 0 rgb(0 0 0 / 0.1)`, `0 1px 2px -1px rgb(0 0 0 / 0.1)`],
  "md": [`0 4px 6px -1px rgb(0 0 0 / 0.1)`, `0 2px 4px -2px rgb(0 0 0 / 0.1)`],
  "lg": [`0 10px 15px -3px rgb(0 0 0 / 0.1)`, `0 4px 6px -4px rgb(0 0 0 / 0.1)`],
  "xl": [`0 20px 25px -5px rgb(0 0 0 / 0.1)`, `0 8px 10px -6px rgb(0 0 0 / 0.1)`],
  "2xl": `0 25px 50px -12px rgb(0 0 0 / 0.25)`,
  "none": "0 0 rgb(0 0 0 / 0)",
  /* @deprecated see: https://github.com/tailwindlabs/tailwindcss/blob/bea843c90ab77b47622200daafb918f7044d9f88/packages/tailwindcss/theme.css#L458 */
  "inner": "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)"
};
const insetShadow = {
  "2xs": "inset 0 1px rgb(0 0 0 / 0.05)",
  "xs": "inset 0 1px 1px rgb(0 0 0 / 0.05)",
  "sm": "inset 0 2px 4px rgb(0 0 0 / 0.05)",
  "none": "0 0 rgb(0 0 0 / 0)"
};
const dropShadow = {
  /* @deprecated see: https://github.com/tailwindlabs/tailwindcss/blob/bea843c90ab77b47622200daafb918f7044d9f88/packages/tailwindcss/theme.css#L459 */
  "DEFAULT": ["0 1px 2px rgb(0 0 0 / 0.1)", "0 1px 1px rgb(0 0 0 / 0.06)"],
  "xs": "0 1px 1px rgb(0 0 0 / 0.05)",
  "sm": "0 1px 2px rgb(0 0 0 / 0.15)",
  "md": "0 3px 3px rgb(0 0 0 / 0.12)",
  "lg": "0 4px 4px rgb(0 0 0 / 0.15)",
  "xl": "0 9px 7px rgb(0 0 0 / 0.1)",
  "2xl": "0 25px 25px rgb(0 0 0 / 0.15)"
};
const textShadow = {
  "none": "0 0 rgb(0 0 0 / 0)",
  "2xs": "0 1px 0 rgb(0 0 0 / 0.15)",
  "xs": "0 1px 1px rgb(0 0 0 / 0.2)",
  "sm": ["0 1px 0 rgb(0 0 0 / 0.075)", "0 1px 1px rgb(0 0 0 / 0.075)", "0 2px 2px rgb(0 0 0 / 0.075)"],
  "md": ["0 1px 1px rgb(0 0 0 / 0.1)", "0 1px 2px rgb(0 0 0 / 0.1)", "0 2px 4px rgb(0 0 0 / 0.1)"],
  "lg": ["0 1px 2px rgb(0 0 0 / 0.1)", "0 3px 2px rgb(0 0 0 / 0.1)", "0 4px 8px rgb(0 0 0 / 0.1)"]
};
const perspective = {
  dramatic: "100px",
  near: "300px",
  normal: "500px",
  midrange: "800px",
  distant: "1200px"
};
const defaults = {
  transition: {
    duration: "150ms",
    timingFunction: "cubic-bezier(0.4, 0, 0.2, 1)"
  },
  font: {
    family: "var(--font-sans)",
    featureSettings: "var(--font-sans--font-feature-settings)",
    variationSettings: "var(--font-sans--font-variation-settings)"
  },
  monoFont: {
    family: "var(--font-mono)",
    featureSettings: "var(--font-mono--font-feature-settings)",
    variationSettings: "var(--font-mono--font-variation-settings)"
  }
};

const container = {
  "3xs": "16rem",
  "2xs": "18rem",
  "xs": "20rem",
  "sm": "24rem",
  "md": "28rem",
  "lg": "32rem",
  "xl": "36rem",
  "2xl": "42rem",
  "3xl": "48rem",
  "4xl": "56rem",
  "5xl": "64rem",
  "6xl": "72rem",
  "7xl": "80rem",
  "prose": "65ch"
};
const breakpoint = {
  "sm": "40rem",
  "md": "48rem",
  "lg": "64rem",
  "xl": "80rem",
  "2xl": "96rem"
};
const verticalBreakpoint = { ...breakpoint };

const supports = {
  grid: "(display: grid)"
};

const ease = {
  "linear": "linear",
  "in": "cubic-bezier(0.4, 0, 1, 1)",
  "out": "cubic-bezier(0, 0, 0.2, 1)",
  "in-out": "cubic-bezier(0.4, 0, 0.2, 1)",
  "DEFAULT": "cubic-bezier(0.4, 0, 0.2, 1)"
};
const property = {
  none: "none",
  all: "all",
  colors: ["color", "background-color", "border-color", "text-decoration-color", "fill", "stroke", "--un-gradient-from", "--un-gradient-via", "--un-gradient-to"].join(","),
  opacity: "opacity",
  shadow: "box-shadow",
  transform: ["transform", "translate", "scale", "rotate"].join(","),
  get DEFAULT() {
    return [this.colors, this.opacity, this.shadow, this.transform, "filter", "-webkit-backdrop-filter", "backdrop-filter"].join(",");
  }
};

const theme = {
  // for rules
  font,
  colors,
  spacing,
  breakpoint,
  verticalBreakpoint,
  text,
  fontWeight,
  tracking,
  leading,
  textStrokeWidth,
  radius,
  shadow,
  insetShadow,
  dropShadow,
  textShadow,
  ease,
  animation,
  blur,
  perspective,
  property,
  default: defaults,
  // for rules & variants
  container,
  // for variant
  aria,
  media,
  supports
};

export { animation, aria, blur, breakpoint, colors, container, defaults, dropShadow, ease, font, fontWeight, insetShadow, leading, media, perspective, property, radius, shadow, spacing, supports, text, textShadow, textStrokeWidth, theme, tracking, verticalBreakpoint };
