{"name": "@unocss/preset-wind4", "type": "module", "version": "66.5.1", "description": "Tailwind 4 compact preset for UnoCSS", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-presets/preset-wind4"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset", "tailwind4"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./rules": {"types": "./dist/rules.d.mts", "default": "./dist/rules.mjs"}, "./theme": {"types": "./dist/theme.d.mts", "default": "./dist/theme.mjs"}, "./colors": {"types": "./dist/colors.d.mts", "default": "./dist/colors.mjs"}, "./shortcuts": {"types": "./dist/shortcuts.d.mts", "default": "./dist/shortcuts.mjs"}, "./variants": {"types": "./dist/variants.d.mts", "default": "./dist/variants.mjs"}, "./postprocess": {"types": "./dist/postprocess.d.mts", "default": "./dist/postprocess.mjs"}, "./utils": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}, "./*": "./*"}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["*.css", "*.d.ts", "dist"], "dependencies": {"@unocss/core": "66.5.1", "@unocss/extractor-arbitrary-variants": "66.5.1", "@unocss/rule-utils": "66.5.1"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}