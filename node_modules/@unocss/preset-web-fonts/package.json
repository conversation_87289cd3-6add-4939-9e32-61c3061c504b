{"name": "@unocss/preset-web-fonts", "type": "module", "version": "66.5.1", "description": "Web Fonts support for Uno CSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-presets/preset-web-fonts"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset", "fonts", "web-fonts", "google-fonts"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./local": {"types": "./dist/local.d.mts", "default": "./dist/local.mjs"}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["*.css", "dist"], "dependencies": {"ofetch": "^1.4.1", "@unocss/core": "66.5.1"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}