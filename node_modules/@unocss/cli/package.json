{"name": "@unocss/cli", "type": "module", "version": "66.5.1", "description": "CLI for UnoCSS", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://johannschopplich.com"}, "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-engine/cli"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": [], "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": {"unocss": "./bin/unocss.mjs"}, "files": ["bin", "dist"], "engines": {"node": ">=14"}, "dependencies": {"@jridgewell/remapping": "^2.3.5", "cac": "^6.7.14", "chokidar": "^3.6.0", "colorette": "^2.0.20", "consola": "^3.4.2", "magic-string": "^0.30.18", "pathe": "^2.0.3", "perfect-debounce": "^1.0.0", "tinyglobby": "^0.2.14", "unplugin-utils": "^0.3.0", "@unocss/core": "66.5.1", "@unocss/config": "66.5.1", "@unocss/preset-uno": "66.5.1"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}