{"name": "@unocss/core", "type": "module", "version": "66.5.1", "description": "The instant on-demand Atomic CSS engine.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-engine/core"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "atomic-css", "atomic-css-engine", "css", "tailwind", "windicss"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "devDependencies": {"magic-string": "^0.30.18", "unconfig": "^7.3.3"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}