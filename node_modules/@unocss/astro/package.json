{"name": "@unocss/astro", "type": "module", "version": "66.5.1", "description": "UnoCSS integration for Astro", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-integrations/astro"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "astro", "astro-integration", "astro-component", "css", "ui"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "peerDependencies": {"vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0"}, "peerDependenciesMeta": {"vite": {"optional": true}}, "dependencies": {"@unocss/core": "66.5.1", "@unocss/reset": "66.5.1", "@unocss/vite": "66.5.1"}, "devDependencies": {"astro": "^4.16.19"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}