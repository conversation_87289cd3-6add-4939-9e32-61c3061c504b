import{s as q,t as B,d as W,x as G,y as H,r as w,u as K,z as J,A as L,B as Q,c as E,C as F,k as l,o as k,g as _,q as j,w as V,e as p,D as X,i as R,E as C,F as Y,j as M,_ as Z,G as z,m as tt,n as O,l as et,h as rt,v as st,P as lt,H as nt,I as ot}from"./index-DgzTQ4fb.js";const D=/^(?!.*\[[^:]+:.+\]$)((?:.+:)?!?)(.*)$/;function at(e={}){const h=e.prefix??"un-",s=e.prefixedOnly??!1,y=e.trueToNonValued??!1;let i;return{name:"attributify",match(n,{generator:b}){const o=q(n);if(!o)return;let r=o[1];if(r.startsWith(h))r=r.slice(h.length);else if(s)return;const u=o[2],[,d="",f=u]=u.match(D)||[];if(f==="~"||y&&f==="true"||!f)return`${d}${r}`;if(i==null){const c=b?.config?.separators?.join("|");c?i=new RegExp(`^(.*\\](?:${c}))(\\[[^\\]]+?\\])$`):i=!1}if(i){const[,c,t]=u.match(i)||[];if(t)return`${c}${d}${r}-${t}`}if(d&&f.match(/^[\d.]+$/)){const c=d.split(/([^:]*:)/g).filter(Boolean),t=c.pop()+f,a=c.join("");return[{matcher:`${d}${r}-${f}`},{matcher:`${a}${r}-${t}`}]}return`${d}${r}-${f}`}}}const it=/(<\w[\w:.$-]*\s)((?:'[^>']*'|"[^>"]*"|`[^>`]*`|\{[^>}]*\}|[^>]*?)*)/g,ct=/(\?|(?!\d|-{2}|-\d)[\w\u00A0-\uFFFF-:%]+)(?:=("[^"]*|'[^']*))?/g,I=/[\s'"`;>]+/;function ut(e){return{name:"attributify",extract:({content:h,cursor:s})=>{const y=h.matchAll(it);let i,n=0;for(const m of y){const[,x,g]=m,A=m.index+x.length;if(s>A&&s<=A+g.length){n=A,i=g;break}}if(!i)return null;const b=i.matchAll(ct);let o=0,r,u;for(const m of b){const[x,g,A]=m,P=n+m.index;if(s>P&&s<=P+x.length){o=P,r=g,u=A?.slice(1);break}}if(!r||r==="class"||r==="className"||r===":class")return null;const d=!!e?.prefix&&r.startsWith(e.prefix);if(e?.prefixedOnly&&!d)return null;const f=d?r.slice(e.prefix.length):r;if(u===void 0)return{extracted:f,resolveReplacement(m){const x=d?e.prefix.length:0;return{start:o+x,end:o+r.length,replacement:m}}};const c=o+r.length+2;let t=I.exec(u),a=0,$;for(;t;){const[m]=t;if(s>c+a&&s<=c+a+t.index){$=u.slice(a,a+t.index);break}a+=t.index+m.length,t=I.exec(u.slice(a))}$===void 0&&($=u.slice(a));const[,v="",S]=$.match(D)||[];return{extracted:`${v}${f}-${S}`,transformSuggestions(m){return m.filter(x=>x.startsWith(`${v}${f}-`)).map(x=>v+x.slice(v.length+f.length+1))},resolveReplacement(m){return{start:a+c,end:a+c+$.length,replacement:v+m.slice(v.length+f.length+1)}}}}}}const dt=["v-bind:",":"],T=/[\s'"`;]+/g,N=/<[^>\s]*\s((?:'[^']*'|"[^"]*"|`[^`]*`|\{[^}]*\}|=>|[^>]*?)*)/g,ft=/(\?|(?!\d|-{2}|-\d)[\w\u00A0-\uFFFF:!%.~<-]+)=?(?:"([^"]*)"|'([^']*)'|\{([^}]*)\})?/g,U=["placeholder","fill","opacity","stroke-opacity"];function mt(e){const h=e?.ignoreAttributes??U,s=e?.nonValuedAttribute??!0,y=e?.trueToNonValued??!1;return{name:"@unocss/preset-attributify/extractor",extract({code:i}){return Array.from(i.matchAll(N)).flatMap(n=>Array.from((n[1]||"").matchAll(ft))).flatMap(([,n,...b])=>{const o=b.filter(Boolean).join("");if(h.includes(n))return[];for(const r of dt)if(n.startsWith(r)){n=n.slice(r.length);break}if(!o){if(B(n)&&s!==!1){const r=[`[${n}=""]`];return y&&r.push(`[${n}="true"]`),r}return[]}return["class","className"].includes(n)?o.split(T).filter(B):N.test(o)?(N.lastIndex=0,this.extract({code:o})):e?.prefixedOnly&&e.prefix&&!n.startsWith(e.prefix)?[]:o.split(T).filter(r=>!!r&&r!==":").map(r=>`[${n}~="${r}"]`)})}}}const pt=(e={})=>{e.strict=e.strict??!1,e.prefix=e.prefix??"un-",e.prefixedOnly=e.prefixedOnly??!1,e.nonValuedAttribute=e.nonValuedAttribute??!0,e.ignoreAttributes=e.ignoreAttributes??U;const h=[at(e)],s=[mt(e)],y=[ut(e)];return{name:"@unocss/preset-attributify",enforce:"post",variants:h,extractors:s,options:e,autocomplete:{extractors:y},extractorDefault:e.strict?!1:void 0}},ht={key:0,"h-full":"","of-hidden":"",flex:"","flex-col":""},xt={p:"4",grid:"~ cols-4 gap-4"},yt={key:0,"row-span-3":""},vt={key:0,"h-full":"","of-hidden":""},gt=W({__name:"ModuleInfo",props:{id:{}},setup(e){const h=e,{data:s}=G(H(h,"id")),y=h.id.split(/\./g).pop(),i=w(null),n=K(i,"module-scrolls");function b(){fetch(`/__open-in-editor?file=${encodeURIComponent(h.id)}`)}const{extractors:o}=pt({strict:!0}),r=J(async()=>{const c=new Set;if(o){const t={code:s.value?.code||""};for(const a of o)(await a.extract(t))?.forEach(v=>c.add(v))}return Array.from(c).filter(t=>!t.startsWith("[")).filter(t=>!s.value?.matched?.some(({rawSelector:a})=>a===t))},[]),u=w(!1),d=w("source"),f=L(Q(()=>s.value?.css),u);return(c,t)=>{const a=X,$=Y,v=Z,S=tt,m=et,x=nt;return l(s)?(k(),E("div",ht,[_(v,{ref_key:"status",ref:i,p0:""},{default:V(()=>[p("div",xt,[p("div",null,[t[3]||(t[3]=p("div",{op50:""}," Module ",-1)),p("a",{"cursor-pointer":"",op80:"","hover:op100":"",onClick:b},[_(a,{id:l(s).id,"mr-1":""},null,8,["id"]),t[2]||(t[2]=p("div",{"i-carbon-launch":""},null,-1))])]),p("div",null,[t[4]||(t[4]=p("div",{op50:""}," Matched Rules ",-1)),R(" "+C(l(s).matched.length),1)]),p("div",null,[t[5]||(t[5]=p("div",{op50:""}," CSS Size ",-1)),R(" "+C(((l(s)?.gzipSize||0)/1024).toFixed(2))+" KiB ",1),t[6]||(t[6]=p("span",{op50:""},"gzipped",-1))]),l(r).length?(k(),E("div",yt,[t[7]||(t[7]=p("div",{op50:""}," Potentially Unmatched ",-1)),p("code",null,C(l(r).join(", ")),1)])):F("",!0)]),_($,{modelValue:l(d),"onUpdate:modelValue":t[0]||(t[0]=g=>M(d)?d.value=g:null)},null,8,["modelValue"])]),_:1},512),l(d)==="source"?(k(),E("div",vt,[_(l(lt),null,{default:V(()=>[_(l(z),{size:"50"},{default:V(()=>[_(S,{"h-full":"","model-value":l(s).code,"read-only":!0,mode:l(y),matched:l(s).matched?.map(({rawSelector:g})=>g),class:"scrolls module-scrolls",style:O(l(n))},null,8,["model-value","mode","matched","style"])]),_:1}),_(l(z),{size:"50"},{default:V(()=>[p("div",null,[_(m,{border:"l b gray-400/20",title:"Output CSS"},{default:V(()=>[p("label",null,[rt(p("input",{"onUpdate:modelValue":t[1]||(t[1]=g=>M(u)?u.value=g:null),type:"checkbox"},null,512),[[st,l(u)]]),t[8]||(t[8]=R(" Prettify ",-1))])]),_:1}),_(S,{"h-full":"",border:"l main","model-value":l(f),"read-only":!0,mode:"css",class:"scrolls module-scrolls",style:O(l(n))},null,8,["model-value","style"])])]),_:1})]),_:1})])):(k(),j(x,{key:1,"flex-grow":"","overflow-y-auto":"",selectors:l(s).matched,icons:l(s).icons,colors:l(s).colors},null,8,["selectors","icons","colors"]))])):F("",!0)}}}),$t=W({__name:"[id]",setup(e){const h=ot();return(s,y)=>{const i=gt;return k(),j(i,{id:l(h).params.id},null,8,["id"])}}});export{$t as default};
