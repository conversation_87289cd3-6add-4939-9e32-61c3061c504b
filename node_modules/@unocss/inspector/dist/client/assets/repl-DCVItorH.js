import{d as g,r as h,u as x,a as S,b,f as k,c as V,o as _,e as s,g as a,w as i,_ as B,h as w,i as C,j as m,v as R,k as l,l as P,n as p,m as E,p as N,q as U}from"./index-DgzTQ4fb.js";const $={"h-full":"",grid:"~ rows-[max-content_1fr]","of-hidden":""},I={"h-full":"","of-hidden":"",grid:"","grid-cols-2":""},L=g({__name:"ReplPlayground",setup(f){const r=h(null),t=x(r,"repl-scrolls"),o=S("unocss:inspector:repl",`<div class="text-sm hover:text-red">
Hello World
</div>`),n=b("unocss-inspector-safelist",!1),{data:c}=k(o,n);return(j,e)=>{const y=B,v=P,u=E;return _(),V("div",$,[s("div",{ref_key:"status",ref:r},[a(y,null,{default:i(()=>[...e[2]||(e[2]=[s("div",null," REPL Playground ",-1),s("div",{op60:""}," Edit your code below to test and play UnoCSS's matching and generating. ",-1)])]),_:1}),a(v,{border:"b gray-400/20",title:""},{default:i(()=>[s("label",null,[w(s("input",{"onUpdate:modelValue":e[0]||(e[0]=d=>m(n)?n.value=d:null),type:"checkbox"},null,512),[[R,l(n)]]),e[3]||(e[3]=C(" Include safelist ",-1))])]),_:1})],512),s("div",I,[a(u,{modelValue:l(o),"onUpdate:modelValue":e[1]||(e[1]=d=>m(o)?o.value=d:null),mode:"html",matched:l(c)?.matched||[],class:"scrolls repl-scrolls",style:p(l(t))},null,8,["modelValue","matched","style"]),a(u,{border:"l main","model-value":l(c)?.css||"/* empty */","read-only":!0,mode:"css",class:"scrolls repl-scrolls",style:p(l(t))},null,8,["model-value","style"])])])}}}),M={};function T(f,r){const t=L;return _(),U(t)}const z=N(M,[["render",T]]);export{z as default};
