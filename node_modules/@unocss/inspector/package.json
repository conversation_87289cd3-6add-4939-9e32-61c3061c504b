{"name": "@unocss/inspector", "type": "module", "version": "66.5.1", "description": "The inspector UI for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-integrations/inspector"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "inspector", "debugger"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"colorette": "^2.0.20", "gzip-size": "^6.0.0", "sirv": "^3.0.1", "vue-flow-layout": "^0.2.0", "@unocss/rule-utils": "66.5.1", "@unocss/core": "66.5.1"}, "scripts": {"build": "unbuild", "build-post": "vite build", "stub": "unbuild --stub", "dev": "nr stub && vite", "update-post": "vite build", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}