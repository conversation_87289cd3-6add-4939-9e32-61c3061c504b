{"name": "@unocss/preset-attributify", "type": "module", "version": "66.5.1", "description": "Attributify preset for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-presets/preset-attributify"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"@unocss/core": "66.5.1"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}