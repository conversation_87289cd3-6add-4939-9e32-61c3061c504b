{"name": "@unocss/vite", "type": "module", "version": "66.5.1", "description": "The Vite plugin for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-integrations/vite"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "vite", "vite-plugin"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./client": {"types": "./dist/client.d.mts", "default": "./dist/client.mjs"}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["dist"], "peerDependencies": {"vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0"}, "dependencies": {"@jridgewell/remapping": "^2.3.5", "chokidar": "^3.6.0", "magic-string": "^0.30.18", "pathe": "^2.0.3", "tinyglobby": "^0.2.14", "unplugin-utils": "^0.3.0", "@unocss/config": "66.5.1", "@unocss/inspector": "66.5.1", "@unocss/core": "66.5.1"}, "devDependencies": {"vite": "^7.1.3"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}