{"name": "@unocss/preset-icons", "type": "module", "version": "66.5.1", "description": "Pure CSS Icons for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://unocss.dev", "repository": {"type": "git", "url": "git+https://github.com/unocss/unocss.git", "directory": "packages-presets/preset-icons"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset", "icons", "css-icons", "iconify"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./browser": {"types": "./dist/browser.d.mts", "default": "./dist/browser.mjs"}, "./core": {"types": "./dist/core.d.mts", "default": "./dist/core.mjs"}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["*.css", "dist"], "dependencies": {"@iconify/utils": "^3.0.1", "ofetch": "^1.4.1", "@unocss/core": "66.5.1"}, "devDependencies": {"@iconify/types": "^2.0.0"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test:attw": "attw --pack --config-path ../../.attw-esm-only.json"}}