import * as _unocss_core from '@unocss/core';
import { UniversalIconLoader } from '@iconify/utils';
import { IconsOptions } from './core.mjs';
export { IconsAPI, combineLoaders, createCDNFetchLoader, createPresetIcons, icons, parseIconWithLoader } from './core.mjs';
import '@iconify/types';
import '@iconify/utils/lib/svg/encode-svg-for-css';

/**
 * Use any icon with Pure CSS for UnoCSS.
 *
 * @example
 *
 * ```html
 * <div class="i-mdi-alarm"></div>
 * <div class="i-logos-vue text-3xl"></div>
 * <button class="i-carbon-sun dark:i-carbon-moon"></div>
 * ```
 *
 * @see https://unocss.dev/presets/icons
 */
declare const presetIcons: _unocss_core.PresetFactory<object, IconsOptions>;
declare function createNodeLoader(): Promise<UniversalIconLoader | undefined>;

export { IconsOptions, createNodeLoader, presetIcons as default, presetIcons };
