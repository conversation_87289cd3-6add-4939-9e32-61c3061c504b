{"name": "@iconify/utils", "type": "module", "description": "Common functions for working with Iconify icon sets used by various packages.", "author": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.0.1", "license": "MIT", "bugs": "https://github.com/iconify/iconify/issues", "homepage": "https://iconify.design/docs/libraries/utils/", "repository": {"type": "git", "url": "https://github.com/iconify/iconify.git", "directory": "packages/utils"}, "sideEffects": false, "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": "./lib/index.js", "./*": "./*", "./lib/colors": "./lib/colors/index.js", "./lib/colors/index": "./lib/colors/index.js", "./lib/colors/keywords": "./lib/colors/keywords.js", "./lib/colors/types": "./lib/colors/types.js", "./lib/css/common": "./lib/css/common.js", "./lib/css/format": "./lib/css/format.js", "./lib/css/icon": "./lib/css/icon.js", "./lib/css/icons": "./lib/css/icons.js", "./lib/css/types": "./lib/css/types.js", "./lib/customisations/bool": "./lib/customisations/bool.js", "./lib/customisations/defaults": "./lib/customisations/defaults.js", "./lib/customisations/flip": "./lib/customisations/flip.js", "./lib/customisations/merge": "./lib/customisations/merge.js", "./lib/customisations/rotate": "./lib/customisations/rotate.js", "./lib/emoji/regex/base": "./lib/emoji/regex/base.js", "./lib/emoji/regex/create": "./lib/emoji/regex/create.js", "./lib/emoji/regex/numbers": "./lib/emoji/regex/numbers.js", "./lib/emoji/regex/similar": "./lib/emoji/regex/similar.js", "./lib/emoji/regex/tree": "./lib/emoji/regex/tree.js", "./lib/emoji/replace/find": "./lib/emoji/replace/find.js", "./lib/emoji/replace/replace": "./lib/emoji/replace/replace.js", "./lib/emoji/test/components": "./lib/emoji/test/components.js", "./lib/emoji/test/missing": "./lib/emoji/test/missing.js", "./lib/emoji/test/name": "./lib/emoji/test/name.js", "./lib/emoji/test/parse": "./lib/emoji/test/parse.js", "./lib/emoji/test/tree": "./lib/emoji/test/tree.js", "./lib/emoji/test/similar": "./lib/emoji/test/similar.js", "./lib/emoji/test/variations": "./lib/emoji/test/variations.js", "./lib/emoji/cleanup": "./lib/emoji/cleanup.js", "./lib/emoji/convert": "./lib/emoji/convert.js", "./lib/emoji/data": "./lib/emoji/data.js", "./lib/emoji/format": "./lib/emoji/format.js", "./lib/emoji/parse": "./lib/emoji/parse.js", "./lib/icon-set/convert-info": "./lib/icon-set/convert-info.js", "./lib/icon-set/expand": "./lib/icon-set/expand.js", "./lib/icon-set/get-icon": "./lib/icon-set/get-icon.js", "./lib/icon-set/get-icons": "./lib/icon-set/get-icons.js", "./lib/icon-set/minify": "./lib/icon-set/minify.js", "./lib/icon-set/parse": "./lib/icon-set/parse.js", "./lib/icon-set/tree": "./lib/icon-set/tree.js", "./lib/icon-set/validate": "./lib/icon-set/validate.js", "./lib/icon-set/validate-basic": "./lib/icon-set/validate-basic.js", "./lib/icon/defaults": "./lib/icon/defaults.js", "./lib/icon/merge": "./lib/icon/merge.js", "./lib/icon/name": "./lib/icon/name.js", "./lib/icon/square": "./lib/icon/square.js", "./lib/icon/transformations": "./lib/icon/transformations.js", "./lib": "./lib/index.js", "./lib/index": "./lib/index.js", "./lib/loader/custom": "./lib/loader/custom.js", "./lib/loader/external-pkg": "./lib/loader/external-pkg.js", "./lib/loader/fs": "./lib/loader/fs.js", "./lib/loader/install-pkg": "./lib/loader/install-pkg.js", "./lib/loader/loader": "./lib/loader/loader.js", "./lib/loader/modern": "./lib/loader/modern.js", "./lib/loader/node-loader": "./lib/loader/node-loader.js", "./lib/loader/node-loaders": "./lib/loader/node-loaders.js", "./lib/loader/types": "./lib/loader/types.js", "./lib/loader/utils": "./lib/loader/utils.js", "./lib/loader/warn": "./lib/loader/warn.js", "./lib/misc/strings": "./lib/misc/strings.js", "./lib/misc/title": "./lib/misc/title.js", "./lib/misc/licenses": "./lib/misc/licenses.js", "./lib/misc/objects": "./lib/misc/objects.js", "./lib/svg/build": "./lib/svg/build.js", "./lib/svg/defs": "./lib/svg/defs.js", "./lib/svg/encode-svg-for-css": "./lib/svg/encode-svg-for-css.js", "./lib/svg/html": "./lib/svg/html.js", "./lib/svg/id": "./lib/svg/id.js", "./lib/svg/inner-html": "./lib/svg/inner-html.js", "./lib/svg/parse": "./lib/svg/parse.js", "./lib/svg/pretty": "./lib/svg/pretty.js", "./lib/svg/size": "./lib/svg/size.js", "./lib/svg/trim": "./lib/svg/trim.js", "./lib/svg/url": "./lib/svg/url.js", "./lib/svg/viewbox": "./lib/svg/viewbox.js"}, "files": ["lib", "*.d.ts"], "dependencies": {"@antfu/install-pkg": "^1.1.0", "@antfu/utils": "^9.2.0", "debug": "^4.4.1", "globals": "^15.15.0", "kolorist": "^1.8.0", "local-pkg": "^1.1.1", "mlly": "^1.7.4", "@iconify/types": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@iconify-json/fa6-regular": "^1.2.4", "@iconify-json/flat-color-icons": "^1.2.1", "@types/debug": "^4.1.12", "@types/jest": "^29.5.14", "@types/node": "^18.19.120", "@typescript-eslint/eslint-plugin": "^8.38.0", "eslint": "^9.31.0", "tsdown": "^0.13.0", "typescript": "^5.8.3", "vitest": "^2.1.9"}, "scripts": {"lint": "eslint --fix src/**/*.ts", "prebuild": "pnpm run lint", "build": "tsdown", "test": "node ./scripts/prepare-tests.mjs && vitest"}}