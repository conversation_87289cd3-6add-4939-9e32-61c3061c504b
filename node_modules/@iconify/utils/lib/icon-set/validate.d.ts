import { IconifyJSON } from "@iconify/types";
/**
 * Match character
 */
declare const matchChar: RegExp;
interface IconSetValidationOptions {
  fix?: boolean;
  prefix?: string;
  provider?: string;
}
/**
 * Validate icon set, return it as IconifyJSON type on success, throw error on failure
 */
declare function validateIconSet(obj: unknown, options?: IconSetValidationOptions): IconifyJSON;
export { IconSetValidationOptions, matchChar, validateIconSet };