import { colorToString, compareColors, stringToColor } from "./colors/index.js";
import { colorKeywords } from "./colors/keywords.js";
import { getIconCSS, getIconContentCSS } from "./css/icon.js";
import { getIconsCSS, getIconsContentCSS } from "./css/icons.js";
import { toBoolean } from "./customisations/bool.js";
import { FullIconCustomisations, IconifyIconCustomisations, IconifyIconSize, IconifyIconSizeCustomisations, defaultIconCustomisations, defaultIconSizeCustomisations } from "./customisations/defaults.js";
import { flipFromString } from "./customisations/flip.js";
import { mergeCustomisations } from "./customisations/merge.js";
import { rotateFromString } from "./customisations/rotate.js";
import { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from "./emoji/cleanup.js";
import { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number } from "./emoji/convert.js";
import { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString } from "./emoji/format.js";
import { prepareEmojiForIconSet, prepareEmojiForIconsList } from "./emoji/parse.js";
import { createOptimisedRegex, createOptimisedRegexForEmojiSequences } from "./emoji/regex/create.js";
import { findAndReplaceEmojisInText } from "./emoji/replace/replace.js";
import { parseEmojiTestFile } from "./emoji/test/parse.js";
import { findMissingEmojis } from "./emoji/test/missing.js";
import { getQualifiedEmojiVariations } from "./emoji/test/variations.js";
import { convertIconSetInfo } from "./icon-set/convert-info.js";
import { expandIconSet } from "./icon-set/expand.js";
import { getIconData } from "./icon-set/get-icon.js";
import { getIcons } from "./icon-set/get-icons.js";
import { minifyIconSet } from "./icon-set/minify.js";
import { parseIconSet, parseIconSetAsync } from "./icon-set/parse.js";
import { ParentIconsList, ParentIconsTree, getIconsTree } from "./icon-set/tree.js";
import { quicklyValidateIconSet } from "./icon-set/validate-basic.js";
import { validateIconSet } from "./icon-set/validate.js";
import { FullExtendedIconifyIcon, FullIconifyIcon, IconifyIcon, PartialExtendedIconifyIcon, defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations } from "./icon/defaults.js";
import { mergeIconData } from "./icon/merge.js";
import { IconifyIconName, IconifyIconSource, matchIconName, stringToIcon, validateIconName } from "./icon/name.js";
import { SVGViewBox, getSVGViewBox } from "./svg/viewbox.js";
import { makeIconSquare } from "./icon/square.js";
import { mergeIconTransformations } from "./icon/transformations.js";
import { IconifyIconBuildResult, iconToSVG } from "./svg/build.js";
import { mergeDefsAndContent, splitSVGDefs, wrapSVGContent } from "./svg/defs.js";
import { replaceIDs } from "./svg/id.js";
import { calculateSize } from "./svg/size.js";
import { encodeSvgForCss } from "./svg/encode-svg-for-css.js";
import { trimSVG } from "./svg/trim.js";
import { prettifySVG } from "./svg/pretty.js";
import { iconToHTML } from "./svg/html.js";
import { svgToData, svgToURL } from "./svg/url.js";
import { cleanUpInnerHTML } from "./svg/inner-html.js";
import { ParsedSVGContent, buildParsedSVG, convertParsedSVG, parseSVGContent } from "./svg/parse.js";
import { CustomCollections, CustomIconLoader, ExternalPkgName, IconCustomizations, IconCustomizer, IconifyLoaderOptions, InlineCollection, UniversalIconLoader } from "./loader/types.js";
import { mergeIconProps } from "./loader/utils.js";
import { getCustomIcon } from "./loader/custom.js";
import { searchForIcon } from "./loader/modern.js";
import { loadIcon } from "./loader/loader.js";
import { camelToKebab, camelize, pascalize, snakelize } from "./misc/strings.js";
import { commonObjectProps, compareObjects, unmergeObjects } from "./misc/objects.js";
import { sanitiseTitleAttribute } from "./misc/title.js";
export { type CustomCollections, type CustomIconLoader, type ExternalPkgName, type FullExtendedIconifyIcon, type FullIconCustomisations, type FullIconifyIcon, type IconCustomizations, type IconCustomizer, type IconifyIcon, type IconifyIconBuildResult, type IconifyIconCustomisations, type IconifyIconName, type IconifyIconSize, type IconifyIconSizeCustomisations, type IconifyIconSource, type IconifyLoaderOptions, type InlineCollection, type ParentIconsList, type ParentIconsTree, type ParsedSVGContent, type PartialExtendedIconifyIcon, type SVGViewBox, type UniversalIconLoader, buildParsedSVG, calculateSize, camelToKebab, camelize, cleanUpInnerHTML, colorKeywords, colorToString, commonObjectProps, compareColors, compareObjects, convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, convertIconSetInfo, convertParsedSVG, createOptimisedRegex, createOptimisedRegexForEmojiSequences, defaultExtendedIconProps, defaultIconCustomisations, defaultIconDimensions, defaultIconProps, defaultIconSizeCustomisations, defaultIconTransformations, encodeSvgForCss, expandIconSet, findAndReplaceEmojisInText, findMissingEmojis, flipFromString, getCustomIcon, getEmojiCodePoint, getEmojiSequenceFromString, getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicode, getEmojiUnicodeString, getIconCSS, getIconContentCSS, getIconData, getIcons, getIconsCSS, getIconsContentCSS, getIconsTree, getQualifiedEmojiVariations, getSVGViewBox, getUnqualifiedEmojiSequence, iconToHTML, iconToSVG, isUTF32SplitNumber, loadIcon, makeIconSquare, matchIconName, mergeCustomisations, mergeDefsAndContent, mergeIconData, mergeIconProps, mergeIconTransformations, mergeUTF32Numbers, minifyIconSet, parseEmojiTestFile, parseIconSet, parseIconSetAsync, parseSVGContent, pascalize, prepareEmojiForIconSet, prepareEmojiForIconsList, prettifySVG, quicklyValidateIconSet, replaceIDs, rotateFromString, sanitiseTitleAttribute, searchForIcon, snakelize, splitSVGDefs, splitUTF32Number, stringToColor, stringToIcon, svgToData, svgToURL, toBoolean, trimSVG, unmergeObjects, validateIconName, validateIconSet, wrapSVGContent };