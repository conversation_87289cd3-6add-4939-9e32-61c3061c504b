{"name": "@antfu/utils", "type": "module", "version": "9.2.0", "description": "Opinionated collection of common JavaScript / TypeScript utils by @antfu", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/utils.git"}, "bugs": {"url": "https://github.com/antfu/utils/issues"}, "keywords": ["utils"], "sideEffects": false, "exports": {".": "./dist/index.mjs"}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.mts", "files": ["dist"], "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@antfu/ni": "^24.3.0", "@types/node": "^22.13.17", "@types/throttle-debounce": "^5.0.2", "bumpp": "^10.1.0", "eslint": "^9.23.0", "p-limit": "^6.2.0", "throttle-debounce": "5.0.0", "tsx": "^4.19.3", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vite": "^6.2.4", "vitest": "^3.1.1"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "lint-fix": "nr lint --fix", "release": "bumpp && pnpm publish", "start": "tsx src/index.ts", "typecheck": "tsc --noEmit", "test": "vitest"}}