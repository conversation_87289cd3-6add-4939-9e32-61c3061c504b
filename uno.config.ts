import {
  defineConfig,
  presetUno,
  presetWind,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup
} from 'unocss'

export default defineConfig({
  // 🎯 掃描來源檔案
  content: [
    './lib/**/*.dart',
    './web/**/*.html',
  ],

  // 🎨 使用預設 presets
  presets: [
    presetUno(), // 基礎 preset
    presetWind(), // Tailwind CSS 相容
    presetIcons({
      // 圖示支援 (可選)
      collections: {
        carbon: () => import('@iconify-json/carbon/icons.json').then(i => i.default),
        mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default),
      }
    }),
  ],

  // 🎨 自定義主題 (遊牧好點品牌色)
  theme: {
    colors: {
      'nomad-primary': '#015899',
      'nomad-tech': '#3b82f6',
      'nomad-workspace': '#f59e0b',
      'nomad-success': '#10b981',
      'nomad-warning': '#f59e0b',
      'nomad-danger': '#ef4444',
    },
    fontFamily: {
      'retro': ['JetBrains Mono', 'monospace'],
    }
  },

  // 🔧 轉換器
  transformers: [
    transformerDirectives(), // 支援 @apply 等指令
    transformerVariantGroup(), // 支援 hover:(bg-blue-500 text-white)
  ],

  // 🎯 自定義 shortcuts (類似 Tailwind 的 @layer components)
  shortcuts: {
    // 遊牧好點自定義組件
    'nomad-btn': 'px-4 py-2 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
    'nomad-btn-primary': 'nomad-btn bg-nomad-primary text-white hover:bg-blue-700 focus:ring-nomad-primary shadow-md hover:shadow-lg',
    'nomad-btn-secondary': 'nomad-btn bg-nomad-tech text-white hover:bg-blue-600 focus:ring-nomad-tech shadow-md hover:shadow-lg',
    'nomad-btn-outline': 'nomad-btn border-2 border-nomad-primary text-nomad-primary hover:bg-nomad-primary hover:text-white focus:ring-nomad-primary',
    
    'nomad-card': 'bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300',
    'nomad-card-hover': 'nomad-card hover:shadow-xl hover:-translate-y-1',
    
    'nomad-input': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-nomad-primary focus:border-nomad-primary',
    
    'nomad-badge': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
    'nomad-badge-primary': 'nomad-badge bg-nomad-primary text-white',
    'nomad-badge-warning': 'nomad-badge bg-nomad-workspace text-white',
  },

  // 🔍 開發模式設定
  inspector: true, // 啟用 UnoCSS Inspector

  // 📊 統計資訊
  details: true,
})
