import 'package:jaspr/jaspr.dart';
import '../components/hero_section.dart';
import '../components/location_card.dart';

class UnoCSSShowcase extends StatelessComponent {
  const UnoCSSShowcase();

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div([
      
      // Hero 區塊
      const HeroSection(),
      
      // 地點卡片展示區
      section(classes: 'py-16 bg-gray-50', [
        div(classes: 'container mx-auto px-4', [
          
          // 標題
          div(classes: 'text-center mb-12', [
            h2(classes: 'text-3xl font-bold text-nomad-primary mb-4', [
              Text('精選遊牧地點')
            ]),
            p(classes: 'text-lg text-gray-600', [
              Text('使用 UnoCSS 打造的現代化卡片設計')
            ])
          ]),
          
          // 卡片網格
          div(classes: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8', [
            
            LocationCard(
              name: 'Cafe Nomad',
              area: '大安區',
              description: '專為數位遊牧者設計的咖啡廳，提供高速 WiFi、舒適座位和無限插座。',
              score: 9.2,
              featured: true,
            ),
            
            LocationCard(
              name: 'Code & Coffee',
              area: '信義區',
              description: '結合程式設計主題的咖啡空間，24小時營業，適合夜貓子工作者。',
              score: 8.8,
            ),
            
            LocationCard(
              name: 'Remote Hub',
              area: '松山區',
              description: '共享工作空間與咖啡廳的完美結合，提供會議室和列印服務。',
              score: 9.0,
            ),
            
            LocationCard(
              name: 'Digital Oasis',
              area: '中山區',
              description: '安靜的工作環境，專業級咖啡，是專注工作的理想選擇。',
              score: 8.5,
              featured: true,
            ),
            
            LocationCard(
              name: 'Startup Cafe',
              area: '內湖區',
              description: '創業者聚集地，經常有networking活動和技術分享會。',
              score: 8.7,
            ),
            
            LocationCard(
              name: 'Freelancer\'s Paradise',
              area: '士林區',
              description: '自由工作者的天堂，提供多樣化的工作空間和美味餐點。',
              score: 9.1,
            ),
            
          ])
          
        ])
      ]),
      
      // 功能展示區
      section(classes: 'py-16 bg-white', [
        div(classes: 'container mx-auto px-4', [
          
          div(classes: 'text-center mb-12', [
            h2(classes: 'text-3xl font-bold text-nomad-primary mb-4', [
              Text('UnoCSS 整合成果')
            ]),
            p(classes: 'text-lg text-gray-600', [
              Text('展示完整的設計系統和組件庫')
            ])
          ]),
          
          div(classes: 'grid grid-cols-1 md:grid-cols-3 gap-8', [
            
            // 效能優勢
            div(classes: 'nomad-card p-6 text-center', [
              div(classes: 'text-4xl mb-4', [Text('⚡')]),
              h3(classes: 'text-xl font-bold text-nomad-primary mb-2', [
                Text('極速建置')
              ]),
              p(classes: 'text-gray-600', [
                Text('比 Tailwind 快 5-10x 的建置速度')
              ])
            ]),
            
            // 檔案大小
            div(classes: 'nomad-card p-6 text-center', [
              div(classes: 'text-4xl mb-4', [Text('📦')]),
              h3(classes: 'text-xl font-bold text-nomad-primary mb-2', [
                Text('更小體積')
              ]),
              p(classes: 'text-gray-600', [
                Text('按需產生，CSS 檔案僅 44KB')
              ])
            ]),
            
            // 開發體驗
            div(classes: 'nomad-card p-6 text-center', [
              div(classes: 'text-4xl mb-4', [Text('🎨')]),
              h3(classes: 'text-xl font-bold text-nomad-primary mb-2', [
                Text('設計自由')
              ]),
              p(classes: 'text-gray-600', [
                Text('自定義 shortcuts 和主題系統')
              ])
            ]),
            
          ])
          
        ])
      ])
      
    ]);
  }
}
