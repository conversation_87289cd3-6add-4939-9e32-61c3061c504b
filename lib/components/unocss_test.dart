import 'package:jaspr/jaspr.dart';

class UnoCSSTest extends StatelessComponent {
  const UnoCSSTest();

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'min-h-screen bg-gray-50 p-8', [
      
      div(classes: 'container mx-auto max-w-4xl space-y-8', [
        
        // === 標題區塊 ===
        div(classes: 'text-center mb-12', [
          h1(classes: 'text-4xl font-bold text-nomad-primary mb-4', [
            Text('⚡ UnoCSS + Jaspr 整合測試')
          ]),
          p(classes: 'text-lg text-gray-600', [
            Text('驗證 UnoCSS 所有功能是否正常運作')
          ])
        ]),
        
        // === 按鈕測試 ===
        div(classes: 'nomad-card p-6 space-y-4', [
          h2(classes: 'text-2xl font-bold text-nomad-primary mb-4', [
            Text('自定義按鈕 Shortcuts')
          ]),
          
          div(classes: 'flex flex-wrap gap-4', [
            button(classes: 'nomad-btn-primary', [Text('Primary 按鈕')]),
            button(classes: 'nomad-btn-secondary', [Text('Secondary 按鈕')]),
            button(classes: 'nomad-btn-outline', [Text('Outline 按鈕')]),
          ]),
          
          div(classes: 'flex flex-wrap gap-2 mt-4', [
            span(classes: 'nomad-badge-primary', [Text('✨ 主要標籤')]),
            span(classes: 'nomad-badge-warning', [Text('⚠️ 警告標籤')]),
          ])
        ]),
        
        // === 品牌色彩測試 ===
        div(classes: 'nomad-card p-6 space-y-4', [
          h2(classes: 'text-2xl font-bold text-nomad-primary mb-4', [
            Text('品牌色彩系統')
          ]),
          
          div(classes: 'grid grid-cols-1 md:grid-cols-4 gap-4', [
            div(classes: 'p-4 bg-nomad-primary text-white rounded-lg text-center', [
              Text('nomad-primary')
            ]),
            div(classes: 'p-4 bg-nomad-tech text-white rounded-lg text-center', [
              Text('nomad-tech')
            ]),
            div(classes: 'p-4 bg-nomad-workspace text-white rounded-lg text-center', [
              Text('nomad-workspace')
            ]),
            div(classes: 'p-4 bg-nomad-success text-white rounded-lg text-center', [
              Text('nomad-success')
            ]),
          ])
        ]),
        
        // === 響應式與動畫測試 ===
        div(classes: 'nomad-card-hover p-6 space-y-4', [
          h2(classes: 'text-2xl font-bold text-nomad-primary mb-4', [
            Text('響應式與動畫效果')
          ]),
          
          div(classes: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4', [
            for (int i = 1; i <= 8; i++)
              div(classes: 'p-4 bg-gradient-to-r from-nomad-tech to-nomad-workspace text-white rounded-lg text-center transform hover:scale-105 transition-transform duration-200', [
                Text('卡片 $i')
              ])
          ]),
          
          p(classes: 'text-sm text-gray-600 mt-4', [
            Text('🎯 滑鼠懸停卡片看縮放效果，調整視窗大小測試響應式')
          ])
        ]),
        
        // === 表單元素測試 ===
        div(classes: 'nomad-card p-6 space-y-4', [
          h2(classes: 'text-2xl font-bold text-nomad-primary mb-4', [
            Text('表單元素')
          ]),
          
          div(classes: 'space-y-4 max-w-md', [
            div([
              label(classes: 'block text-sm font-medium text-gray-700 mb-1', [
                Text('測試輸入框')
              ]),
              input(
                type: InputType.text,
                classes: 'nomad-input',
                attributes: {'placeholder': '輸入測試文字'}
              )
            ]),
            
            div([
              label(classes: 'block text-sm font-medium text-gray-700 mb-1', [
                Text('選擇項目')
              ]),
              select(classes: 'nomad-input', [
                option(value: '1', [Text('選項 1')]),
                option(value: '2', [Text('選項 2')]),
                option(value: '3', [Text('選項 3')]),
              ])
            ])
          ])
        ]),
        
        // === 效能資訊 ===
        div(classes: 'bg-green-50 border border-green-200 rounded-lg p-6', [
          h3(classes: 'text-lg font-semibold text-green-800 mb-2', [
            Text('✅ UnoCSS 效能優勢')
          ]),
          ul(classes: 'text-sm text-green-700 space-y-1', [
            li([Text('• 建置速度比 Tailwind 快 5-10x')]),
            li([Text('• 按需產生，CSS 檔案更小')]),
            li([Text('• 完全相容 Tailwind 語法')]),
            li([Text('• 零設定，開箱即用')]),
            li([Text('• 支援自定義 shortcuts 和主題')]),
          ])
        ])
        
      ])
    ]);
  }
}
