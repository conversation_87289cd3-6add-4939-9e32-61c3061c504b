import 'package:jaspr/jaspr.dart';

class LocationCard extends StatelessComponent {
  final String name;
  final String area;
  final String description;
  final double score;
  final bool featured;

  const LocationCard({
    required this.name,
    required this.area,
    required this.description,
    required this.score,
    this.featured = false,
  });

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'nomad-card-hover relative', [
      
      // 特色標籤
      if (featured)
        div(classes: 'absolute top-4 right-4 z-10 nomad-badge-warning', [
          Text('✨ 編輯推薦')
        ]),
      
      // 主圖片區域
      div(classes: 'h-48 bg-gradient-to-r from-nomad-tech to-nomad-workspace'),
      
      // 內容區域
      div(classes: 'p-6 space-y-4', [
        
        // 地區標籤
        p(classes: 'text-sm text-gray-500', [Text(area)]),
        
        // 店名
        h3(classes: 'text-xl font-bold text-nomad-primary', [Text(name)]),
        
        // 描述
        p(classes: 'text-gray-600 leading-relaxed', [Text(description)]),
        
        // 評分區塊
        div(classes: 'bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100', [
          div(classes: 'flex items-center justify-between', [
            span(classes: 'text-sm font-medium text-gray-600', [Text('遊牧指數')]),
            span(classes: 'text-2xl font-bold text-nomad-tech', [Text('$score/10')])
          ])
        ]),
        
        // 行動按鈕
        div(classes: 'flex justify-end', [
          button(classes: 'nomad-btn-primary', [
            Text('查看詳情 →')
          ])
        ])
        
      ])
    ]);
  }
}
