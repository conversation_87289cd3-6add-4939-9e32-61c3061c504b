import 'package:jaspr/jaspr.dart';

class HeroSection extends StatelessComponent {
  const HeroSection();

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield section(classes: 'min-h-screen bg-gradient-to-br from-nomad-primary via-blue-800 to-indigo-900 flex items-center justify-center text-white', [
      
      div(classes: 'container mx-auto px-4 text-center', [
        
        div(classes: 'max-w-4xl mx-auto space-y-8', [
          
          // 主標題
          h1(classes: 'text-5xl md:text-7xl font-bold leading-tight', [
            Text('台北遊牧者'),
            br(),
            span(classes: 'text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400', [
              Text('終極指南')
            ])
          ]),
          
          // 副標題
          div(classes: 'space-y-2', [
            p(classes: 'text-xl md:text-2xl text-blue-100', [
              Text('10 個我們親身驗證過的咖啡天堂')
            ]),
            p(classes: 'text-sm text-blue-300', [
              Text('2025年9月版 • 基於 UnoCSS 建構')
            ])
          ]),
          
          // CTA 按鈕
          div(classes: 'pt-8', [
            button(classes: 'nomad-btn-primary text-lg px-8 py-4 transform hover:scale-105', [
              Text('開始探索 ↓')
            ])
          ])
          
        ])
      ])
      
    ]);
  }
}
